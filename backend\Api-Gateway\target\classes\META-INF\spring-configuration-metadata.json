{"groups": [{"name": "rate-limiting", "type": "com.PFE2025.Api_Gateway.config.RateLimitConfig", "sourceType": "com.PFE2025.Api_Gateway.config.RateLimitConfig"}, {"name": "rate-limiting.default-config", "type": "com.PFE2025.Api_Gateway.config.RateLimitConfig$DefaultConfig", "sourceType": "com.PFE2025.Api_Gateway.config.RateLimitConfig", "sourceMethod": "public com.PFE2025.Api_Gateway.config.RateLimitConfig.DefaultConfig getDefaultConfig() "}], "properties": [{"name": "rate-limiting.default-config.duration", "type": "java.lang.Integer", "sourceType": "com.PFE2025.Api_Gateway.config.RateLimitConfig$DefaultConfig"}, {"name": "rate-limiting.default-config.limit", "type": "java.lang.Integer", "sourceType": "com.PFE2025.Api_Gateway.config.RateLimitConfig$DefaultConfig"}, {"name": "rate-limiting.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.PFE2025.Api_Gateway.config.RateLimitConfig"}, {"name": "rate-limiting.endpoints", "type": "java.util.List<com.PFE2025.Api_Gateway.config.RateLimitConfig$EndpointConfig>", "sourceType": "com.PFE2025.Api_Gateway.config.RateLimitConfig"}], "hints": []}