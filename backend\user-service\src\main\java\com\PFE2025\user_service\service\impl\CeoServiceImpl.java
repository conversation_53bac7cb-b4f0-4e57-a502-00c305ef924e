package com.PFE2025.user_service.service.impl;

import com.PFE2025.user_service.dto.request.CeoCreateRequest;
import com.PFE2025.user_service.dto.request.CeoUpdateRequest;
import com.PFE2025.user_service.dto.response.CeoProfileResponse;
import com.PFE2025.user_service.exception.ResourceNotFoundException;
import com.PFE2025.user_service.exception.ResourceAlreadyExistsException;
import com.PFE2025.user_service.model.CeoProfile;
import com.PFE2025.user_service.repository.CeoProfileRepository;
import com.PFE2025.user_service.service.CeoService;
import com.PFE2025.user_service.util.CeoProfileMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * Implémentation du service pour la gestion des profils CEO.
 * Fournit toutes les opérations CRUD et de recherche.
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CeoServiceImpl implements CeoService {

    private final CeoProfileRepository ceoProfileRepository;
    private final CeoProfileMapper ceoProfileMapper;

    @Override
    public CeoProfileResponse createCeoProfile(CeoCreateRequest request, String keycloakId) {
        log.debug("Creating CEO profile for existing keycloakId: {}", keycloakId);

        // Vérifier si un profil existe déjà
        if (ceoProfileRepository.existsByKeycloakId(keycloakId)) {
            log.error("CEO profile already exists for keycloakId: {}", keycloakId);
            throw new ResourceAlreadyExistsException("CEO profile already exists for this user");
        }

        // Créer le profil avec les données de base depuis la requête
        CeoProfile profile = ceoProfileMapper.toEntity(request);
        profile.setKeycloakId(keycloakId);
        profile.setRoles(Arrays.asList("CEO"));
        profile.setCreatedAt(LocalDateTime.now());
        profile.setUpdatedAt(LocalDateTime.now());

        // Sauvegarder
        CeoProfile savedProfile = ceoProfileRepository.save(profile);
        log.info("CEO profile created successfully with id: {} for keycloakId: {}", savedProfile.getId(), keycloakId);

        return ceoProfileMapper.toResponse(savedProfile);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CeoProfileResponse> getAllCeos(Pageable pageable) {
        log.debug("Getting all CEO profiles with pagination: {}", pageable);
        
        Page<CeoProfile> profiles = ceoProfileRepository.findAll(pageable);
        return profiles.map(ceoProfileMapper::toResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public CeoProfileResponse getCeoById(String id) {
        log.debug("Getting CEO profile by id: {}", id);
        
        CeoProfile profile = ceoProfileRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("CEO profile not found with id: " + id));
        
        return ceoProfileMapper.toResponse(profile);
    }

    @Override
    @Transactional(readOnly = true)
    public CeoProfileResponse getCeoByKeycloakId(String keycloakId) {
        log.debug("Getting CEO profile by keycloakId: {}", keycloakId);
        
        CeoProfile profile = ceoProfileRepository.findByKeycloakId(keycloakId)
                .orElseThrow(() -> new ResourceNotFoundException("CEO profile not found for keycloakId: " + keycloakId));
        
        return ceoProfileMapper.toResponse(profile);
    }

    @Override
    public CeoProfileResponse updateCeo(String id, CeoUpdateRequest request) {
        log.debug("Updating CEO profile with id: {}", id);
        
        CeoProfile profile = ceoProfileRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("CEO profile not found with id: " + id));
        
        // Mettre à jour les champs
        ceoProfileMapper.updateEntity(profile, request);
        profile.setUpdatedAt(LocalDateTime.now());
        
        // Sauvegarder
        CeoProfile updatedProfile = ceoProfileRepository.save(profile);
        log.info("CEO profile updated successfully with id: {}", updatedProfile.getId());
        
        return ceoProfileMapper.toResponse(updatedProfile);
    }

    @Override
    public void deleteCeo(String id) {
        log.debug("Deleting CEO profile with id: {}", id);
        
        if (!ceoProfileRepository.existsById(id)) {
            throw new ResourceNotFoundException("CEO profile not found with id: " + id);
        }
        
        ceoProfileRepository.deleteById(id);
        log.info("CEO profile deleted successfully with id: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CeoProfileResponse> searchCeos(String keyword, Pageable pageable) {
        log.debug("Searching CEO profiles with keyword: {}", keyword);

        Page<CeoProfile> profiles = ceoProfileRepository.searchByKeyword(keyword, pageable);
        return profiles.map(ceoProfileMapper::toResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CeoProfileResponse> getCeosByLocation(String location, Pageable pageable) {
        log.debug("Getting CEO profiles by location: {}", location);
        
        Page<CeoProfile> profiles = ceoProfileRepository.findByLocationContainingIgnoreCase(location, pageable);
        return profiles.map(ceoProfileMapper::toResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CeoProfileResponse> getCeosByCompany(String companyName, Pageable pageable) {
        log.debug("Getting CEO profiles by company: {}", companyName);
        
        Page<CeoProfile> profiles = ceoProfileRepository.findByCompanyNameContainingIgnoreCase(companyName, pageable);
        return profiles.map(ceoProfileMapper::toResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CeoProfileResponse> getCeosByExperience(Integer minYears, Pageable pageable) {
        log.debug("Getting CEO profiles with minimum experience: {} years", minYears);
        
        Page<CeoProfile> profiles = ceoProfileRepository.findByYearsAsLeaderGreaterThanEqual(minYears, pageable);
        return profiles.map(ceoProfileMapper::toResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CeoProfileResponse> getCeosByEmployeeCount(Integer minEmployees, Pageable pageable) {
        log.debug("Getting CEO profiles managing minimum {} employees", minEmployees);
        
        Page<CeoProfile> profiles = ceoProfileRepository.findByTotalEmployeesGreaterThanEqual(minEmployees, pageable);
        return profiles.map(ceoProfileMapper::toResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CeoProfileResponse> getCeosByIndustry(String industry, Pageable pageable) {
        log.debug("Getting CEO profiles by industry: {}", industry);
        
        Page<CeoProfile> profiles = ceoProfileRepository.findByIndustriesContaining(industry, pageable);
        return profiles.map(ceoProfileMapper::toResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public long countCeos() {
        log.debug("Counting total CEO profiles");
        return ceoProfileRepository.count();
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByKeycloakId(String keycloakId) {
        return ceoProfileRepository.existsByKeycloakId(keycloakId);
    }

    /**
     * Méthode optimisée pour les recherches avec projection limitée
     */
    @Transactional(readOnly = true)
    public Page<CeoProfileResponse> searchCeoProfilesOptimized(String keyword, Pageable pageable) {
        log.debug("Optimized search for CEO profiles with keyword: {}", keyword);

        // Utilise la même recherche mais avec une approche optimisée
        Page<CeoProfile> profiles = ceoProfileRepository.searchByKeyword(keyword, pageable);

        // Conversion optimisée avec le mapper
        return profiles.map(profile -> {
            CeoProfileResponse response = ceoProfileMapper.toResponse(profile);
            // On peut ici limiter les champs si nécessaire pour les listes
            return response;
        });
    }

}
