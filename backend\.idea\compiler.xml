<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="api-gateway" />
        <module name="ai-processing-service" />
        <module name="document-management-service" />
        <module name="auth-service" />
        <module name="discovery-service" />
      </profile>
      <profile name="Annotation profile for job-requisition-service" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok-mapstruct-binding/0.2.0/lombok-mapstruct-binding-0.2.0.jar" />
        </processorPath>
        <module name="job-requisition-service" />
        <module name="job-posting-service" />
      </profile>
      <profile name="Annotation profile for user-service" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <option name="mapstruct.defaultComponentModel" value="spring" />
        <processorPath useClasspath="false">
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok-mapstruct-binding/0.2.0/lombok-mapstruct-binding-0.2.0.jar" />
        </processorPath>
        <module name="interview-service" />
        <module name="application-service" />
        <module name="user-service" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="Api-Gateway" target="17" />
      <module name="candidate-service" target="17" />
      <module name="document-Management-service" target="17" />
      <module name="document-service" target="17" />
      <module name="document_Management_service" target="17" />
      <module name="Document_Management_Service" target="17" />
      <module name="job-requisiting-service" target="17" />
      <module name="user-service (1)" target="17" />
      <module name="user-service (2)" target="17" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="Api-Gateway" options="-parameters" />
      <module name="Document_Management_Service" options="-parameters" />
      <module name="ai-processing-service" options="-parameters" />
      <module name="api-gateway" options="-parameters" />
      <module name="application-service" options="-parameters -Amapstruct.defaultComponentModel=spring" />
      <module name="auth-service" options="-parameters" />
      <module name="candidate-service" options="-parameters -Amapstruct.defaultComponentModel=spring" />
      <module name="discovery-service" options="-parameters" />
      <module name="document-Management-service" options="-parameters" />
      <module name="document-management-service" options="-parameters" />
      <module name="document-service" options="-parameters" />
      <module name="document_Management_service" options="-parameters" />
      <module name="interview-service" options="-parameters -Amapstruct.defaultComponentModel=spring" />
      <module name="job-posting-service" options="-parameters" />
      <module name="job-requisiting-service" options="-parameters" />
      <module name="job-requisition-service" options="-parameters" />
      <module name="user-service" options="-parameters -Amapstruct.defaultComponentModel=spring" />
      <module name="user-service (1)" options="-parameters -Amapstruct.defaultComponentModel=spring" />
      <module name="user-service (2)" options="-parameters -Amapstruct.defaultComponentModel=spring" />
    </option>
  </component>
</project>