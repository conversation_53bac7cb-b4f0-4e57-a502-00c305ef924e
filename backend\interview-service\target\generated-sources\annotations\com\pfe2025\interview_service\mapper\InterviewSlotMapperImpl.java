package com.pfe2025.interview_service.mapper;

import com.pfe2025.interview_service.dto.InterviewSlotDTO;
import com.pfe2025.interview_service.model.InterviewSlot;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T14:17:52+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class InterviewSlotMapperImpl implements InterviewSlotMapper {

    @Override
    public InterviewSlotDTO.SlotResponse toResponse(InterviewSlot slot) {
        if ( slot == null ) {
            return null;
        }

        InterviewSlotDTO.SlotResponse.SlotResponseBuilder slotResponse = InterviewSlotDTO.SlotResponse.builder();

        slotResponse.slotStatus( slot.getStatus() );
        slotResponse.slotFormat( slot.getFormat() );
        slotResponse.cancellationReason( slot.getCancellationReason() );
        slotResponse.endDateTime( slot.getEndDateTime() );
        slotResponse.googleCalendarEventId( slot.getGoogleCalendarEventId() );
        slotResponse.id( slot.getId() );
        slotResponse.location( slot.getLocation() );
        slotResponse.meetingLink( slot.getMeetingLink() );
        slotResponse.startDateTime( slot.getStartDateTime() );

        return slotResponse.build();
    }

    @Override
    public List<InterviewSlotDTO.SlotResponse> toResponseList(List<InterviewSlot> slots) {
        if ( slots == null ) {
            return null;
        }

        List<InterviewSlotDTO.SlotResponse> list = new ArrayList<InterviewSlotDTO.SlotResponse>( slots.size() );
        for ( InterviewSlot interviewSlot : slots ) {
            list.add( toResponse( interviewSlot ) );
        }

        return list;
    }

    @Override
    public InterviewSlot fromRequest(InterviewSlotDTO.CreateRequest request) {
        if ( request == null ) {
            return null;
        }

        InterviewSlot.InterviewSlotBuilder<?, ?> interviewSlot = InterviewSlot.builder();

        interviewSlot.endDateTime( request.getEndDateTime() );
        interviewSlot.format( request.getFormat() );
        interviewSlot.location( request.getLocation() );
        interviewSlot.meetingLink( request.getMeetingLink() );
        interviewSlot.startDateTime( request.getStartDateTime() );

        interviewSlot.status( InterviewSlot.SlotStatus.PROPOSED );
        interviewSlot.createdAt( java.time.LocalDateTime.now() );

        return interviewSlot.build();
    }
}
