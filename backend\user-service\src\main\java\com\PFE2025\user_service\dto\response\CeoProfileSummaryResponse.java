package com.PFE2025.user_service.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO léger pour les listes de profils CEO (optimisation performance)
 * Contient uniquement les champs essentiels pour l'affichage en liste
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CeoProfileSummaryResponse {
    
    private String id;
    private String keycloakId;
    
    // Informations de base
    private String firstName;
    private String lastName;
    private String fullName;
    private String email;
    private String status;
    
    // Informations professionnelles essentielles
    private String companyName;
    private String location;
    private String industry;
    private Integer yearsAsLeader;
    private Integer totalEmployees;
    
    // Photo pour l'affichage
    private String profilePicture;
    
    // Métadonnées
    private String createdAt;
    private String updatedAt;
}
