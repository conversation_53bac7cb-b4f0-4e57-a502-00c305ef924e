package com.PFE2025.user_service.performance;

import com.PFE2025.user_service.dto.request.CeoCreateRequest;
import com.PFE2025.user_service.dto.response.CeoProfileResponse;
import com.PFE2025.user_service.service.CeoService;
import com.PFE2025.user_service.service.CandidateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.util.StopWatch;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Tests de performance pour les opérations de profils
 */
@SpringBootTest
@TestPropertySource(properties = {
    "spring.data.mongodb.database=test_user_service_perf",
    "eureka.client.enabled=false",
    "spring.cloud.stream.bindings.processCvParsedEvent-in-0.destination=test-ai-results"
})
class ProfilePerformanceTest {

    @Autowired
    private CeoService ceoService;

    @Autowired
    private CandidateService candidateService;

    @BeforeEach
    void setUp() {
        // Créer des données de test pour les performances
        createTestData();
    }

    @Test
    void testCeoSearchPerformance() {
        StopWatch stopWatch = new StopWatch();
        
        // Test de recherche avec pagination
        stopWatch.start("CEO Search");
        Page<CeoProfileResponse> results = ceoService.searchCeos("Tech", PageRequest.of(0, 20));
        stopWatch.stop();
        
        // Vérifications
        assertThat(results).isNotNull();
        assertThat(stopWatch.getLastTaskTimeMillis()).isLessThan(1000); // Moins de 1 seconde
        
        System.out.println("CEO Search Performance: " + stopWatch.getLastTaskTimeMillis() + "ms");
    }

    @Test
    void testCeoListingPerformance() {
        StopWatch stopWatch = new StopWatch();
        
        // Test de listing avec pagination
        stopWatch.start("CEO Listing");
        Page<CeoProfileResponse> results = ceoService.getAllCeos(PageRequest.of(0, 50));
        stopWatch.stop();
        
        // Vérifications
        assertThat(results).isNotNull();
        assertThat(stopWatch.getLastTaskTimeMillis()).isLessThan(500); // Moins de 500ms
        
        System.out.println("CEO Listing Performance: " + stopWatch.getLastTaskTimeMillis() + "ms");
    }

    @Test
    void testCandidateSearchPerformance() {
        StopWatch stopWatch = new StopWatch();
        
        // Test de recherche candidats
        stopWatch.start("Candidate Search");
        var results = candidateService.searchCandidates("Java", PageRequest.of(0, 20));
        stopWatch.stop();
        
        // Vérifications
        assertThat(results).isNotNull();
        assertThat(stopWatch.getLastTaskTimeMillis()).isLessThan(1000); // Moins de 1 seconde
        
        System.out.println("Candidate Search Performance: " + stopWatch.getLastTaskTimeMillis() + "ms");
    }

    @Test
    void testConcurrentAccess() throws InterruptedException {
        int numberOfThreads = 10;
        Thread[] threads = new Thread[numberOfThreads];
        long[] executionTimes = new long[numberOfThreads];
        
        // Créer des threads pour tester l'accès concurrent
        for (int i = 0; i < numberOfThreads; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                
                // Opération de lecture
                ceoService.getAllCeos(PageRequest.of(0, 10));
                
                stopWatch.stop();
                executionTimes[threadIndex] = stopWatch.getTotalTimeMillis();
            });
        }
        
        // Démarrer tous les threads
        for (Thread thread : threads) {
            thread.start();
        }
        
        // Attendre la fin de tous les threads
        for (Thread thread : threads) {
            thread.join();
        }
        
        // Vérifier les performances
        long averageTime = 0;
        for (long time : executionTimes) {
            averageTime += time;
        }
        averageTime /= numberOfThreads;
        
        assertThat(averageTime).isLessThan(1000); // Temps moyen < 1 seconde
        System.out.println("Average concurrent access time: " + averageTime + "ms");
    }

    private void createTestData() {
        // Créer quelques profils CEO pour les tests
        for (int i = 0; i < 5; i++) {
            CeoCreateRequest request = CeoCreateRequest.builder()
                    .firstName("CEO" + i)
                    .lastName("Test" + i)
                    .email("ceo" + i + "@test.com")
                    .phone("+3312345678" + i)
                    .location("City" + i)
                    .companyName("Tech Company " + i)
                    .industries(Arrays.asList("Technology"))
                    .yearsAsLeader(5 + i)
                    .totalEmployees(100 * (i + 1))
                    .password("SecurePass123!")
                    .build();
            
            try {
                ceoService.createCeoProfile(request, "test-keycloak-" + i);
            } catch (Exception e) {
                // Ignorer si déjà existant
            }
        }
    }
}
