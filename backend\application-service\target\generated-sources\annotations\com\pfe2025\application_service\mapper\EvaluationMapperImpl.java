package com.pfe2025.application_service.mapper;

import com.pfe2025.application_service.dto.EvaluationDTO;
import com.pfe2025.application_service.event.ApplicationEvaluatedEvent;
import com.pfe2025.application_service.model.Evaluation;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T02:01:20+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class EvaluationMapperImpl extends EvaluationMapper {

    @Override
    public EvaluationDTO toEvaluationDTO(Evaluation evaluation) {
        if ( evaluation == null ) {
            return null;
        }

        EvaluationDTO.EvaluationDTOBuilder evaluationDTO = EvaluationDTO.builder();

        evaluationDTO.evaluatedAt( evaluation.getCreatedAt() );
        evaluationDTO.categoryScores( jsonToMap( evaluation.getCategoryScores() ) );
        evaluationDTO.educationScore( evaluation.getEducationScore() );
        evaluationDTO.exceededAutoThreshold( evaluation.getExceededAutoThreshold() );
        evaluationDTO.experienceScore( evaluation.getExperienceScore() );
        evaluationDTO.id( evaluation.getId() );
        evaluationDTO.justification( evaluation.getJustification() );
        evaluationDTO.modelUsed( evaluation.getModelUsed() );
        evaluationDTO.overallScore( evaluation.getOverallScore() );
        evaluationDTO.recommendation( evaluation.getRecommendation() );
        evaluationDTO.softSkillScore( evaluation.getSoftSkillScore() );
        evaluationDTO.strengths( evaluation.getStrengths() );
        evaluationDTO.technicalSkillScore( evaluation.getTechnicalSkillScore() );
        evaluationDTO.weaknesses( evaluation.getWeaknesses() );

        return evaluationDTO.build();
    }

    @Override
    public Evaluation fromApplicationEvaluatedEvent(ApplicationEvaluatedEvent event) {
        if ( event == null ) {
            return null;
        }

        Evaluation.EvaluationBuilder<?, ?> evaluation = Evaluation.builder();

        evaluation.categoryScores( mapToJson( event.getCategoryScores() ) );
        evaluation.justification( event.getJustification() );
        evaluation.modelUsed( event.getModelUsed() );
        evaluation.overallScore( event.getOverallScore() );
        evaluation.rawResponse( event.getRawResponse() );
        evaluation.recommendation( event.getRecommendation() );
        evaluation.strengths( event.getStrengths() );
        evaluation.weaknesses( event.getWeaknesses() );

        return evaluation.build();
    }
}
