package com.pfe2025.application_service.mapper;

import com.pfe2025.application_service.dto.AISettingsDTO;
import com.pfe2025.application_service.model.AISettings;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T02:01:21+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AISettingsMapperImpl implements AISettingsMapper {

    @Override
    public AISettingsDTO toDto(AISettings settings) {
        if ( settings == null ) {
            return null;
        }

        AISettingsDTO.AISettingsDTOBuilder aISettingsDTO = AISettingsDTO.builder();

        aISettingsDTO.autoAcceptThreshold( settings.getAutoAcceptThreshold() );
        aISettingsDTO.autoRejectThreshold( settings.getAutoRejectThreshold() );
        aISettingsDTO.automationLevel( settings.getAutomationLevel() );
        aISettingsDTO.department( settings.getDepartment() );
        aISettingsDTO.id( settings.getId() );
        aISettingsDTO.isActive( settings.getIsActive() );
        aISettingsDTO.isSelfCalibrating( settings.getIsSelfCalibrating() );
        aISettingsDTO.jobType( settings.getJobType() );
        aISettingsDTO.lastCalibrationDate( settings.getLastCalibrationDate() );
        aISettingsDTO.reviewThreshold( settings.getReviewThreshold() );

        return aISettingsDTO.build();
    }

    @Override
    public List<AISettingsDTO> toDtoList(List<AISettings> settings) {
        if ( settings == null ) {
            return null;
        }

        List<AISettingsDTO> list = new ArrayList<AISettingsDTO>( settings.size() );
        for ( AISettings aISettings : settings ) {
            list.add( toDto( aISettings ) );
        }

        return list;
    }

    @Override
    public AISettings toEntity(AISettingsDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AISettings.AISettingsBuilder<?, ?> aISettings = AISettings.builder();

        aISettings.id( dto.getId() );
        aISettings.autoAcceptThreshold( dto.getAutoAcceptThreshold() );
        aISettings.autoRejectThreshold( dto.getAutoRejectThreshold() );
        aISettings.automationLevel( dto.getAutomationLevel() );
        aISettings.department( dto.getDepartment() );
        aISettings.isActive( dto.getIsActive() );
        aISettings.isSelfCalibrating( dto.getIsSelfCalibrating() );
        aISettings.jobType( dto.getJobType() );
        aISettings.lastCalibrationDate( dto.getLastCalibrationDate() );
        aISettings.reviewThreshold( dto.getReviewThreshold() );

        return aISettings.build();
    }

    @Override
    public void updateFromDto(AISettingsDTO dto, AISettings settings) {
        if ( dto == null ) {
            return;
        }

        if ( dto.getAutoAcceptThreshold() != null ) {
            settings.setAutoAcceptThreshold( dto.getAutoAcceptThreshold() );
        }
        if ( dto.getAutoRejectThreshold() != null ) {
            settings.setAutoRejectThreshold( dto.getAutoRejectThreshold() );
        }
        if ( dto.getAutomationLevel() != null ) {
            settings.setAutomationLevel( dto.getAutomationLevel() );
        }
        if ( dto.getDepartment() != null ) {
            settings.setDepartment( dto.getDepartment() );
        }
        if ( dto.getIsActive() != null ) {
            settings.setIsActive( dto.getIsActive() );
        }
        if ( dto.getIsSelfCalibrating() != null ) {
            settings.setIsSelfCalibrating( dto.getIsSelfCalibrating() );
        }
        if ( dto.getJobType() != null ) {
            settings.setJobType( dto.getJobType() );
        }
        if ( dto.getLastCalibrationDate() != null ) {
            settings.setLastCalibrationDate( dto.getLastCalibrationDate() );
        }
        if ( dto.getReviewThreshold() != null ) {
            settings.setReviewThreshold( dto.getReviewThreshold() );
        }
    }
}
