package com.pfe2025.jobpostingservice.mapper;

import com.pfe2025.jobpostingservice.dto.MetricsDailySnapshotDTO;
import com.pfe2025.jobpostingservice.dto.PostingMetricsDTO;
import com.pfe2025.jobpostingservice.model.JobPost;
import com.pfe2025.jobpostingservice.model.MetricsDailySnapshot;
import com.pfe2025.jobpostingservice.model.PostingMetrics;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T14:17:57+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class PostingMetricsMapperImpl implements PostingMetricsMapper {

    @Override
    public PostingMetricsDTO toDto(PostingMetrics metrics) {
        if ( metrics == null ) {
            return null;
        }

        PostingMetricsDTO.PostingMetricsDTOBuilder<?, ?> postingMetricsDTO = PostingMetricsDTO.builder();

        postingMetricsDTO.jobPostId( metricsJobPostId( metrics ) );
        postingMetricsDTO.conversionRate( metrics.getConversionRate() );
        postingMetricsDTO.dailySnapshots( toDtoList( metrics.getDailySnapshots() ) );
        postingMetricsDTO.id( metrics.getId() );
        postingMetricsDTO.lastUpdated( metrics.getLastUpdated() );
        postingMetricsDTO.totalApplicationCount( metrics.getTotalApplicationCount() );
        postingMetricsDTO.totalViewCount( metrics.getTotalViewCount() );
        postingMetricsDTO.uniqueViewCount( metrics.getUniqueViewCount() );

        return postingMetricsDTO.build();
    }

    @Override
    public PostingMetrics toEntity(PostingMetricsDTO dto) {
        if ( dto == null ) {
            return null;
        }

        PostingMetrics.PostingMetricsBuilder<?, ?> postingMetrics = PostingMetrics.builder();

        postingMetrics.id( dto.getId() );
        postingMetrics.conversionRate( dto.getConversionRate() );
        postingMetrics.dailySnapshots( metricsDailySnapshotDTOListToMetricsDailySnapshotSet( dto.getDailySnapshots() ) );
        postingMetrics.lastUpdated( dto.getLastUpdated() );
        postingMetrics.totalApplicationCount( dto.getTotalApplicationCount() );
        postingMetrics.totalViewCount( dto.getTotalViewCount() );
        postingMetrics.uniqueViewCount( dto.getUniqueViewCount() );

        return postingMetrics.build();
    }

    @Override
    public MetricsDailySnapshotDTO toDto(MetricsDailySnapshot snapshot) {
        if ( snapshot == null ) {
            return null;
        }

        MetricsDailySnapshotDTO.MetricsDailySnapshotDTOBuilder<?, ?> metricsDailySnapshotDTO = MetricsDailySnapshotDTO.builder();

        metricsDailySnapshotDTO.dailyApplicationCount( snapshot.getDailyApplicationCount() );
        metricsDailySnapshotDTO.dailyUniqueViewCount( snapshot.getDailyUniqueViewCount() );
        metricsDailySnapshotDTO.dailyViewCount( snapshot.getDailyViewCount() );
        metricsDailySnapshotDTO.date( snapshot.getDate() );
        metricsDailySnapshotDTO.id( snapshot.getId() );

        return metricsDailySnapshotDTO.build();
    }

    @Override
    public MetricsDailySnapshot toEntity(MetricsDailySnapshotDTO dto) {
        if ( dto == null ) {
            return null;
        }

        MetricsDailySnapshot.MetricsDailySnapshotBuilder<?, ?> metricsDailySnapshot = MetricsDailySnapshot.builder();

        metricsDailySnapshot.id( dto.getId() );
        metricsDailySnapshot.dailyApplicationCount( dto.getDailyApplicationCount() );
        metricsDailySnapshot.dailyUniqueViewCount( dto.getDailyUniqueViewCount() );
        metricsDailySnapshot.dailyViewCount( dto.getDailyViewCount() );
        metricsDailySnapshot.date( dto.getDate() );

        return metricsDailySnapshot.build();
    }

    private Long metricsJobPostId(PostingMetrics postingMetrics) {
        if ( postingMetrics == null ) {
            return null;
        }
        JobPost jobPost = postingMetrics.getJobPost();
        if ( jobPost == null ) {
            return null;
        }
        Long id = jobPost.getId();
        if ( id == null ) {
            return null;
        }
        return id;
    }

    protected Set<MetricsDailySnapshot> metricsDailySnapshotDTOListToMetricsDailySnapshotSet(List<MetricsDailySnapshotDTO> list) {
        if ( list == null ) {
            return null;
        }

        Set<MetricsDailySnapshot> set = new LinkedHashSet<MetricsDailySnapshot>( Math.max( (int) ( list.size() / .75f ) + 1, 16 ) );
        for ( MetricsDailySnapshotDTO metricsDailySnapshotDTO : list ) {
            set.add( toEntity( metricsDailySnapshotDTO ) );
        }

        return set;
    }
}
