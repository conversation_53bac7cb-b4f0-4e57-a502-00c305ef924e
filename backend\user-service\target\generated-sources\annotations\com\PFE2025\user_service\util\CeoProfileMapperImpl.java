package com.PFE2025.user_service.util;

import com.PFE2025.user_service.dto.request.CeoCreateRequest;
import com.PFE2025.user_service.dto.request.CeoUpdateRequest;
import com.PFE2025.user_service.dto.response.CeoProfileResponse;
import com.PFE2025.user_service.model.CeoProfile;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T14:18:04+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class CeoProfileMapperImpl implements CeoProfileMapper {

    @Override
    public CeoProfileResponse toResponse(CeoProfile profile) {
        if ( profile == null ) {
            return null;
        }

        CeoProfileResponse.CeoProfileResponseBuilder ceoProfileResponse = CeoProfileResponse.builder();

        ceoProfileResponse.annualRevenue( profile.getAnnualRevenue() );
        List<String> list = profile.getBoardMemberships();
        if ( list != null ) {
            ceoProfileResponse.boardMemberships( new ArrayList<String>( list ) );
        }
        List<String> list1 = profile.getCertifications();
        if ( list1 != null ) {
            ceoProfileResponse.certifications( new ArrayList<String>( list1 ) );
        }
        ceoProfileResponse.companyName( profile.getCompanyName() );
        ceoProfileResponse.createdAt( profile.getCreatedAt() );
        ceoProfileResponse.email( profile.getEmail() );
        ceoProfileResponse.firstName( profile.getFirstName() );
        ceoProfileResponse.fullName( profile.getFullName() );
        ceoProfileResponse.id( profile.getId() );
        List<String> list2 = profile.getIndustries();
        if ( list2 != null ) {
            ceoProfileResponse.industries( new ArrayList<String>( list2 ) );
        }
        List<String> list3 = profile.getKeyAchievements();
        if ( list3 != null ) {
            ceoProfileResponse.keyAchievements( new ArrayList<String>( list3 ) );
        }
        ceoProfileResponse.keycloakId( profile.getKeycloakId() );
        ceoProfileResponse.lastName( profile.getLastName() );
        ceoProfileResponse.location( profile.getLocation() );
        ceoProfileResponse.phone( profile.getPhone() );
        List<String> list4 = profile.getPreviousCompanies();
        if ( list4 != null ) {
            ceoProfileResponse.previousCompanies( new ArrayList<String>( list4 ) );
        }
        List<String> list5 = profile.getRoles();
        if ( list5 != null ) {
            ceoProfileResponse.roles( new ArrayList<String>( list5 ) );
        }
        ceoProfileResponse.status( profile.getStatus() );
        List<String> list6 = profile.getStrategicObjectives();
        if ( list6 != null ) {
            ceoProfileResponse.strategicObjectives( new ArrayList<String>( list6 ) );
        }
        ceoProfileResponse.totalEmployees( profile.getTotalEmployees() );
        ceoProfileResponse.updatedAt( profile.getUpdatedAt() );
        ceoProfileResponse.userType( profile.getUserType() );
        ceoProfileResponse.vision( profile.getVision() );
        ceoProfileResponse.yearsAsLeader( profile.getYearsAsLeader() );

        return ceoProfileResponse.build();
    }

    @Override
    public CeoProfile toEntity(CeoCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        CeoProfile.CeoProfileBuilder<?, ?> ceoProfile = CeoProfile.builder();

        ceoProfile.annualRevenue( request.getAnnualRevenue() );
        List<String> list = request.getBoardMemberships();
        if ( list != null ) {
            ceoProfile.boardMemberships( new ArrayList<String>( list ) );
        }
        List<String> list1 = request.getCertifications();
        if ( list1 != null ) {
            ceoProfile.certifications( new ArrayList<String>( list1 ) );
        }
        ceoProfile.companyName( request.getCompanyName() );
        ceoProfile.email( request.getEmail() );
        ceoProfile.firstName( request.getFirstName() );
        List<String> list2 = request.getIndustries();
        if ( list2 != null ) {
            ceoProfile.industries( new ArrayList<String>( list2 ) );
        }
        List<String> list3 = request.getKeyAchievements();
        if ( list3 != null ) {
            ceoProfile.keyAchievements( new ArrayList<String>( list3 ) );
        }
        ceoProfile.lastName( request.getLastName() );
        ceoProfile.location( request.getLocation() );
        ceoProfile.phone( request.getPhone() );
        List<String> list4 = request.getPreviousCompanies();
        if ( list4 != null ) {
            ceoProfile.previousCompanies( new ArrayList<String>( list4 ) );
        }
        List<String> list5 = request.getStrategicObjectives();
        if ( list5 != null ) {
            ceoProfile.strategicObjectives( new ArrayList<String>( list5 ) );
        }
        ceoProfile.totalEmployees( request.getTotalEmployees() );
        ceoProfile.vision( request.getVision() );
        ceoProfile.yearsAsLeader( request.getYearsAsLeader() );

        ceoProfile.userType( "CEO" );
        ceoProfile.status( "ACTIVE" );

        return ceoProfile.build();
    }

    @Override
    public void updateEntity(CeoProfile profile, CeoUpdateRequest request) {
        if ( request == null ) {
            return;
        }

        profile.setAnnualRevenue( request.getAnnualRevenue() );
        if ( profile.getBoardMemberships() != null ) {
            List<String> list = request.getBoardMemberships();
            if ( list != null ) {
                profile.getBoardMemberships().clear();
                profile.getBoardMemberships().addAll( list );
            }
            else {
                profile.setBoardMemberships( null );
            }
        }
        else {
            List<String> list = request.getBoardMemberships();
            if ( list != null ) {
                profile.setBoardMemberships( new ArrayList<String>( list ) );
            }
        }
        if ( profile.getCertifications() != null ) {
            List<String> list1 = request.getCertifications();
            if ( list1 != null ) {
                profile.getCertifications().clear();
                profile.getCertifications().addAll( list1 );
            }
            else {
                profile.setCertifications( null );
            }
        }
        else {
            List<String> list1 = request.getCertifications();
            if ( list1 != null ) {
                profile.setCertifications( new ArrayList<String>( list1 ) );
            }
        }
        profile.setCompanyName( request.getCompanyName() );
        if ( profile.getIndustries() != null ) {
            List<String> list2 = request.getIndustries();
            if ( list2 != null ) {
                profile.getIndustries().clear();
                profile.getIndustries().addAll( list2 );
            }
            else {
                profile.setIndustries( null );
            }
        }
        else {
            List<String> list2 = request.getIndustries();
            if ( list2 != null ) {
                profile.setIndustries( new ArrayList<String>( list2 ) );
            }
        }
        if ( profile.getKeyAchievements() != null ) {
            List<String> list3 = request.getKeyAchievements();
            if ( list3 != null ) {
                profile.getKeyAchievements().clear();
                profile.getKeyAchievements().addAll( list3 );
            }
            else {
                profile.setKeyAchievements( null );
            }
        }
        else {
            List<String> list3 = request.getKeyAchievements();
            if ( list3 != null ) {
                profile.setKeyAchievements( new ArrayList<String>( list3 ) );
            }
        }
        profile.setLocation( request.getLocation() );
        profile.setPhone( request.getPhone() );
        if ( profile.getPreviousCompanies() != null ) {
            List<String> list4 = request.getPreviousCompanies();
            if ( list4 != null ) {
                profile.getPreviousCompanies().clear();
                profile.getPreviousCompanies().addAll( list4 );
            }
            else {
                profile.setPreviousCompanies( null );
            }
        }
        else {
            List<String> list4 = request.getPreviousCompanies();
            if ( list4 != null ) {
                profile.setPreviousCompanies( new ArrayList<String>( list4 ) );
            }
        }
        if ( profile.getStrategicObjectives() != null ) {
            List<String> list5 = request.getStrategicObjectives();
            if ( list5 != null ) {
                profile.getStrategicObjectives().clear();
                profile.getStrategicObjectives().addAll( list5 );
            }
            else {
                profile.setStrategicObjectives( null );
            }
        }
        else {
            List<String> list5 = request.getStrategicObjectives();
            if ( list5 != null ) {
                profile.setStrategicObjectives( new ArrayList<String>( list5 ) );
            }
        }
        profile.setTotalEmployees( request.getTotalEmployees() );
        profile.setVision( request.getVision() );
        profile.setYearsAsLeader( request.getYearsAsLeader() );

        enrichEntity( profile );
    }
}
