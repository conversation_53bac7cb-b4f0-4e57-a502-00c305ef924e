package com.PFE2025.user_service.util;

import com.PFE2025.user_service.dto.request.CeoCreateRequest;
import com.PFE2025.user_service.dto.request.CeoUpdateRequest;
import com.PFE2025.user_service.dto.response.CeoProfileResponse;
import com.PFE2025.user_service.model.CeoProfile;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T02:11:23+0100",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.8 (Oracle Corporation)"
)
@Component
public class CeoProfileMapperImpl implements CeoProfileMapper {

    @Override
    public CeoProfileResponse toResponse(CeoProfile profile) {
        if ( profile == null ) {
            return null;
        }

        CeoProfileResponse.CeoProfileResponseBuilder ceoProfileResponse = CeoProfileResponse.builder();

        ceoProfileResponse.id( profile.getId() );
        ceoProfileResponse.keycloakId( profile.getKeycloakId() );
        ceoProfileResponse.firstName( profile.getFirstName() );
        ceoProfileResponse.lastName( profile.getLastName() );
        ceoProfileResponse.fullName( profile.getFullName() );
        ceoProfileResponse.email( profile.getEmail() );
        List<String> list = profile.getRoles();
        if ( list != null ) {
            ceoProfileResponse.roles( new ArrayList<String>( list ) );
        }
        ceoProfileResponse.userType( profile.getUserType() );
        ceoProfileResponse.status( profile.getStatus() );
        ceoProfileResponse.location( profile.getLocation() );
        ceoProfileResponse.phone( profile.getPhone() );
        ceoProfileResponse.companyName( profile.getCompanyName() );
        ceoProfileResponse.totalEmployees( profile.getTotalEmployees() );
        List<String> list1 = profile.getStrategicObjectives();
        if ( list1 != null ) {
            ceoProfileResponse.strategicObjectives( new ArrayList<String>( list1 ) );
        }
        ceoProfileResponse.yearsAsLeader( profile.getYearsAsLeader() );
        List<String> list2 = profile.getPreviousCompanies();
        if ( list2 != null ) {
            ceoProfileResponse.previousCompanies( new ArrayList<String>( list2 ) );
        }
        List<String> list3 = profile.getCertifications();
        if ( list3 != null ) {
            ceoProfileResponse.certifications( new ArrayList<String>( list3 ) );
        }
        List<String> list4 = profile.getBoardMemberships();
        if ( list4 != null ) {
            ceoProfileResponse.boardMemberships( new ArrayList<String>( list4 ) );
        }
        ceoProfileResponse.vision( profile.getVision() );
        List<String> list5 = profile.getKeyAchievements();
        if ( list5 != null ) {
            ceoProfileResponse.keyAchievements( new ArrayList<String>( list5 ) );
        }
        ceoProfileResponse.annualRevenue( profile.getAnnualRevenue() );
        List<String> list6 = profile.getIndustries();
        if ( list6 != null ) {
            ceoProfileResponse.industries( new ArrayList<String>( list6 ) );
        }
        ceoProfileResponse.createdAt( profile.getCreatedAt() );
        ceoProfileResponse.updatedAt( profile.getUpdatedAt() );

        return ceoProfileResponse.build();
    }

    @Override
    public CeoProfile toEntity(CeoCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        CeoProfile.CeoProfileBuilder<?, ?> ceoProfile = CeoProfile.builder();

        ceoProfile.firstName( request.getFirstName() );
        ceoProfile.lastName( request.getLastName() );
        ceoProfile.email( request.getEmail() );
        ceoProfile.location( request.getLocation() );
        ceoProfile.phone( request.getPhone() );
        ceoProfile.companyName( request.getCompanyName() );
        ceoProfile.totalEmployees( request.getTotalEmployees() );
        List<String> list = request.getStrategicObjectives();
        if ( list != null ) {
            ceoProfile.strategicObjectives( new ArrayList<String>( list ) );
        }
        ceoProfile.yearsAsLeader( request.getYearsAsLeader() );
        List<String> list1 = request.getPreviousCompanies();
        if ( list1 != null ) {
            ceoProfile.previousCompanies( new ArrayList<String>( list1 ) );
        }
        List<String> list2 = request.getCertifications();
        if ( list2 != null ) {
            ceoProfile.certifications( new ArrayList<String>( list2 ) );
        }
        List<String> list3 = request.getBoardMemberships();
        if ( list3 != null ) {
            ceoProfile.boardMemberships( new ArrayList<String>( list3 ) );
        }
        ceoProfile.vision( request.getVision() );
        List<String> list4 = request.getKeyAchievements();
        if ( list4 != null ) {
            ceoProfile.keyAchievements( new ArrayList<String>( list4 ) );
        }
        ceoProfile.annualRevenue( request.getAnnualRevenue() );
        List<String> list5 = request.getIndustries();
        if ( list5 != null ) {
            ceoProfile.industries( new ArrayList<String>( list5 ) );
        }

        ceoProfile.userType( "CEO" );
        ceoProfile.status( "ACTIVE" );

        return ceoProfile.build();
    }

    @Override
    public void updateEntity(CeoProfile profile, CeoUpdateRequest request) {
        if ( request == null ) {
            return;
        }

        profile.setLocation( request.getLocation() );
        profile.setPhone( request.getPhone() );
        profile.setCompanyName( request.getCompanyName() );
        profile.setTotalEmployees( request.getTotalEmployees() );
        if ( profile.getStrategicObjectives() != null ) {
            List<String> list = request.getStrategicObjectives();
            if ( list != null ) {
                profile.getStrategicObjectives().clear();
                profile.getStrategicObjectives().addAll( list );
            }
            else {
                profile.setStrategicObjectives( null );
            }
        }
        else {
            List<String> list = request.getStrategicObjectives();
            if ( list != null ) {
                profile.setStrategicObjectives( new ArrayList<String>( list ) );
            }
        }
        profile.setYearsAsLeader( request.getYearsAsLeader() );
        if ( profile.getPreviousCompanies() != null ) {
            List<String> list1 = request.getPreviousCompanies();
            if ( list1 != null ) {
                profile.getPreviousCompanies().clear();
                profile.getPreviousCompanies().addAll( list1 );
            }
            else {
                profile.setPreviousCompanies( null );
            }
        }
        else {
            List<String> list1 = request.getPreviousCompanies();
            if ( list1 != null ) {
                profile.setPreviousCompanies( new ArrayList<String>( list1 ) );
            }
        }
        if ( profile.getCertifications() != null ) {
            List<String> list2 = request.getCertifications();
            if ( list2 != null ) {
                profile.getCertifications().clear();
                profile.getCertifications().addAll( list2 );
            }
            else {
                profile.setCertifications( null );
            }
        }
        else {
            List<String> list2 = request.getCertifications();
            if ( list2 != null ) {
                profile.setCertifications( new ArrayList<String>( list2 ) );
            }
        }
        if ( profile.getBoardMemberships() != null ) {
            List<String> list3 = request.getBoardMemberships();
            if ( list3 != null ) {
                profile.getBoardMemberships().clear();
                profile.getBoardMemberships().addAll( list3 );
            }
            else {
                profile.setBoardMemberships( null );
            }
        }
        else {
            List<String> list3 = request.getBoardMemberships();
            if ( list3 != null ) {
                profile.setBoardMemberships( new ArrayList<String>( list3 ) );
            }
        }
        profile.setVision( request.getVision() );
        if ( profile.getKeyAchievements() != null ) {
            List<String> list4 = request.getKeyAchievements();
            if ( list4 != null ) {
                profile.getKeyAchievements().clear();
                profile.getKeyAchievements().addAll( list4 );
            }
            else {
                profile.setKeyAchievements( null );
            }
        }
        else {
            List<String> list4 = request.getKeyAchievements();
            if ( list4 != null ) {
                profile.setKeyAchievements( new ArrayList<String>( list4 ) );
            }
        }
        profile.setAnnualRevenue( request.getAnnualRevenue() );
        if ( profile.getIndustries() != null ) {
            List<String> list5 = request.getIndustries();
            if ( list5 != null ) {
                profile.getIndustries().clear();
                profile.getIndustries().addAll( list5 );
            }
            else {
                profile.setIndustries( null );
            }
        }
        else {
            List<String> list5 = request.getIndustries();
            if ( list5 != null ) {
                profile.setIndustries( new ArrayList<String>( list5 ) );
            }
        }

        enrichEntity( profile );
    }
}
