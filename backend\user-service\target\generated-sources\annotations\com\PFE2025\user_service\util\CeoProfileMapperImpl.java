package com.PFE2025.user_service.util;

import com.PFE2025.user_service.dto.request.CeoUpdateRequest;
import com.PFE2025.user_service.dto.response.CeoProfileResponse;
import com.PFE2025.user_service.model.CeoProfile;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T15:22:13+0100",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.8 (Oracle Corporation)"
)
@Component
public class CeoProfileMapperImpl implements CeoProfileMapper {

    @Override
    public CeoProfileResponse toResponse(CeoProfile profile) {
        if ( profile == null ) {
            return null;
        }

        CeoProfileResponse.CeoProfileResponseBuilder ceoProfileResponse = CeoProfileResponse.builder();

        ceoProfileResponse.id( profile.getId() );
        ceoProfileResponse.keycloakId( profile.getKeycloakId() );
        ceoProfileResponse.firstName( profile.getFirstName() );
        ceoProfileResponse.lastName( profile.getLastName() );
        ceoProfileResponse.fullName( profile.getFullName() );
        ceoProfileResponse.email( profile.getEmail() );
        List<String> list = profile.getRoles();
        if ( list != null ) {
            ceoProfileResponse.roles( new ArrayList<String>( list ) );
        }
        ceoProfileResponse.userType( profile.getUserType() );
        ceoProfileResponse.status( profile.getStatus() );
        ceoProfileResponse.location( profile.getLocation() );
        ceoProfileResponse.phone( profile.getPhone() );
        ceoProfileResponse.createdAt( profile.getCreatedAt() );
        ceoProfileResponse.updatedAt( profile.getUpdatedAt() );

        return ceoProfileResponse.build();
    }

    @Override
    public void updateEntity(CeoProfile profile, CeoUpdateRequest request) {
        if ( request == null ) {
            return;
        }

        profile.setLocation( request.getLocation() );
        profile.setPhone( request.getPhone() );

        enrichEntity( profile );
    }
}
