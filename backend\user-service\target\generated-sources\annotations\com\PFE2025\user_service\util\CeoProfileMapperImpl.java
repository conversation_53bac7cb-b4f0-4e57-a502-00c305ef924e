package com.PFE2025.user_service.util;

import com.PFE2025.user_service.dto.request.CeoCreateRequest;
import com.PFE2025.user_service.dto.request.CeoUpdateRequest;
import com.PFE2025.user_service.dto.response.CeoProfileResponse;
import com.PFE2025.user_service.model.CeoProfile;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T14:24:52+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class CeoProfileMapperImpl implements CeoProfileMapper {

    @Override
    public CeoProfileResponse toResponse(CeoProfile profile) {
        if ( profile == null ) {
            return null;
        }

        CeoProfileResponse.CeoProfileResponseBuilder ceoProfileResponse = CeoProfileResponse.builder();

        ceoProfileResponse.createdAt( profile.getCreatedAt() );
        ceoProfileResponse.email( profile.getEmail() );
        ceoProfileResponse.firstName( profile.getFirstName() );
        ceoProfileResponse.fullName( profile.getFullName() );
        ceoProfileResponse.id( profile.getId() );
        ceoProfileResponse.keycloakId( profile.getKeycloakId() );
        ceoProfileResponse.lastName( profile.getLastName() );
        ceoProfileResponse.location( profile.getLocation() );
        ceoProfileResponse.phone( profile.getPhone() );
        List<String> list = profile.getRoles();
        if ( list != null ) {
            ceoProfileResponse.roles( new ArrayList<String>( list ) );
        }
        ceoProfileResponse.status( profile.getStatus() );
        ceoProfileResponse.updatedAt( profile.getUpdatedAt() );
        ceoProfileResponse.userType( profile.getUserType() );

        return ceoProfileResponse.build();
    }

    @Override
    public CeoProfile toEntity(CeoCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        CeoProfile.CeoProfileBuilder<?, ?> ceoProfile = CeoProfile.builder();

        ceoProfile.email( request.getEmail() );
        ceoProfile.firstName( request.getFirstName() );
        ceoProfile.lastName( request.getLastName() );
        ceoProfile.location( request.getLocation() );
        ceoProfile.phone( request.getPhone() );

        ceoProfile.userType( "CEO" );
        ceoProfile.status( "ACTIVE" );

        return ceoProfile.build();
    }

    @Override
    public void updateEntity(CeoProfile profile, CeoUpdateRequest request) {
        if ( request == null ) {
            return;
        }

        profile.setLocation( request.getLocation() );
        profile.setPhone( request.getPhone() );

        enrichEntity( profile );
    }
}
