package com.pfe2025.jobrequisitionservice.mapper;

import com.pfe2025.jobrequisitionservice.dto.JobRequisitionDto;
import com.pfe2025.jobrequisitionservice.dto.JobRequisitionSummaryDTO;
import com.pfe2025.jobrequisitionservice.model.JobRequisition;
import com.pfe2025.jobrequisitionservice.model.RequisitionStatus;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T02:01:55+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class JobRequisitionMapperImpl implements JobRequisitionMapper {

    private final StatusHistoryMapper statusHistoryMapper;

    @Autowired
    public JobRequisitionMapperImpl(StatusHistoryMapper statusHistoryMapper) {

        this.statusHistoryMapper = statusHistoryMapper;
    }

    @Override
    public JobRequisitionDto.Response toResponseDto(JobRequisition entity) {
        if ( entity == null ) {
            return null;
        }

        JobRequisitionDto.Response.ResponseBuilder response = JobRequisitionDto.Response.builder();

        response.statusHistory( statusHistoryMapper.toStatusHistoryDtoSet( entity.getStatusHistory() ) );
        response.ceoId( entity.getCeoId() );
        response.ceoResponseDate( entity.getCeoResponseDate() );
        response.createdAt( entity.getCreatedAt() );
        response.department( entity.getDepartment() );
        response.description( entity.getDescription() );
        response.expectedStartDate( entity.getExpectedStartDate() );
        response.id( entity.getId() );
        response.minExperience( entity.getMinExperience() );
        response.neededHeadcount( entity.getNeededHeadcount() );
        response.projectLeaderId( entity.getProjectLeaderId() );
        response.projectLeaderName( entity.getProjectLeaderName() );
        response.projectName( entity.getProjectName() );
        response.rejectionReason( entity.getRejectionReason() );
        response.requiredLevel( entity.getRequiredLevel() );
        Set<String> set1 = entity.getRequiredSkills();
        if ( set1 != null ) {
            response.requiredSkills( new LinkedHashSet<String>( set1 ) );
        }
        response.status( entity.getStatus() );
        response.title( entity.getTitle() );
        response.updatedAt( entity.getUpdatedAt() );
        if ( entity.getUrgent() != null ) {
            response.urgent( entity.getUrgent() );
        }

        return response.build();
    }

    @Override
    public JobRequisitionSummaryDTO toSummaryDto(JobRequisition entity) {
        if ( entity == null ) {
            return null;
        }

        JobRequisitionSummaryDTO.JobRequisitionSummaryDTOBuilder jobRequisitionSummaryDTO = JobRequisitionSummaryDTO.builder();

        jobRequisitionSummaryDTO.createdAt( entity.getCreatedAt() );
        jobRequisitionSummaryDTO.department( entity.getDepartment() );
        jobRequisitionSummaryDTO.expectedStartDate( entity.getExpectedStartDate() );
        jobRequisitionSummaryDTO.id( entity.getId() );
        jobRequisitionSummaryDTO.neededHeadcount( entity.getNeededHeadcount() );
        jobRequisitionSummaryDTO.status( entity.getStatus() );
        jobRequisitionSummaryDTO.title( entity.getTitle() );
        jobRequisitionSummaryDTO.urgent( entity.getUrgent() );

        jobRequisitionSummaryDTO.requiredLevel( entity.getRequiredLevel().toString() );

        return jobRequisitionSummaryDTO.build();
    }

    @Override
    public JobRequisition toEntity(JobRequisitionDto.Request request) {
        if ( request == null ) {
            return null;
        }

        JobRequisition.JobRequisitionBuilder jobRequisition = JobRequisition.builder();

        jobRequisition.department( request.getDepartment() );
        jobRequisition.description( request.getDescription() );
        jobRequisition.expectedStartDate( request.getExpectedStartDate() );
        jobRequisition.minExperience( request.getMinExperience() );
        jobRequisition.neededHeadcount( request.getNeededHeadcount() );
        jobRequisition.projectName( request.getProjectName() );
        jobRequisition.requiredLevel( request.getRequiredLevel() );
        Set<String> set = request.getRequiredSkills();
        if ( set != null ) {
            jobRequisition.requiredSkills( new LinkedHashSet<String>( set ) );
        }
        jobRequisition.title( request.getTitle() );
        jobRequisition.urgent( request.isUrgent() );

        jobRequisition.status( RequisitionStatus.DRAFT );

        return jobRequisition.build();
    }

    @Override
    public void updateEntityFromDto(JobRequisitionDto.Request request, JobRequisition entity) {
        if ( request == null ) {
            return;
        }

        entity.setDepartment( request.getDepartment() );
        entity.setDescription( request.getDescription() );
        entity.setExpectedStartDate( request.getExpectedStartDate() );
        entity.setMinExperience( request.getMinExperience() );
        entity.setNeededHeadcount( request.getNeededHeadcount() );
        entity.setProjectName( request.getProjectName() );
        entity.setRequiredLevel( request.getRequiredLevel() );
        if ( entity.getRequiredSkills() != null ) {
            Set<String> set = request.getRequiredSkills();
            if ( set != null ) {
                entity.getRequiredSkills().clear();
                entity.getRequiredSkills().addAll( set );
            }
            else {
                entity.setRequiredSkills( null );
            }
        }
        else {
            Set<String> set = request.getRequiredSkills();
            if ( set != null ) {
                entity.setRequiredSkills( new LinkedHashSet<String>( set ) );
            }
        }
        entity.setTitle( request.getTitle() );
        entity.setUrgent( request.isUrgent() );
    }

    @Override
    public List<JobRequisitionDto.Response> toResponseDtoList(List<JobRequisition> entities) {
        if ( entities == null ) {
            return null;
        }

        List<JobRequisitionDto.Response> list = new ArrayList<JobRequisitionDto.Response>( entities.size() );
        for ( JobRequisition jobRequisition : entities ) {
            list.add( toResponseDto( jobRequisition ) );
        }

        return list;
    }

    @Override
    public List<JobRequisitionSummaryDTO> toSummaryDtoList(List<JobRequisition> entities) {
        if ( entities == null ) {
            return null;
        }

        List<JobRequisitionSummaryDTO> list = new ArrayList<JobRequisitionSummaryDTO>( entities.size() );
        for ( JobRequisition jobRequisition : entities ) {
            list.add( toSummaryDto( jobRequisition ) );
        }

        return list;
    }
}
