package com.pfe2025.interview_service.mapper;

import com.pfe2025.interview_service.dto.FeedbackDTO;
import com.pfe2025.interview_service.dto.InterviewDTO;
import com.pfe2025.interview_service.dto.InterviewSlotDTO;
import com.pfe2025.interview_service.dto.ParticipantDTO;
import com.pfe2025.interview_service.event.InterviewRequestedEvent;
import com.pfe2025.interview_service.model.Interview;
import com.pfe2025.interview_service.model.InterviewFeedback;
import com.pfe2025.interview_service.model.InterviewParticipant;
import com.pfe2025.interview_service.model.InterviewSlot;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T14:17:52+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class InterviewMapperImpl implements InterviewMapper {

    @Autowired
    private InterviewSlotMapper interviewSlotMapper;
    @Autowired
    private ParticipantMapper participantMapper;
    @Autowired
    private FeedbackMapper feedbackMapper;

    @Override
    public InterviewDTO.DetailResponse toDetailResponse(Interview interview) {
        if ( interview == null ) {
            return null;
        }

        InterviewDTO.DetailResponse.DetailResponseBuilder detailResponse = InterviewDTO.DetailResponse.builder();

        detailResponse.scheduleDate( interview.getScheduledAt() );
        detailResponse.currentStatus( interview.getStatus() );
        detailResponse.applicationId( interview.getApplicationId() );
        detailResponse.applicationReference( interview.getApplicationReference() );
        detailResponse.candidateId( interview.getCandidateId() );
        detailResponse.candidateName( interview.getCandidateName() );
        detailResponse.createdAt( interview.getCreatedAt() );
        detailResponse.description( interview.getDescription() );
        detailResponse.feedbackSummary( interview.getFeedbackSummary() );
        detailResponse.feedbacks( interviewFeedbackSetToFeedbackResponseList( interview.getFeedbacks() ) );
        detailResponse.format( interview.getFormat() );
        detailResponse.id( interview.getId() );
        detailResponse.isRecommended( interview.getIsRecommended() );
        detailResponse.jobDepartment( interview.getJobDepartment() );
        detailResponse.jobPostingId( interview.getJobPostingId() );
        detailResponse.jobTitle( interview.getJobTitle() );
        detailResponse.location( interview.getLocation() );
        detailResponse.meetingLink( interview.getMeetingLink() );
        detailResponse.overallScore( interview.getOverallScore() );
        detailResponse.participants( interviewParticipantSetToParticipantResponseList( interview.getParticipants() ) );
        detailResponse.slots( interviewSlotSetToSlotResponseList( interview.getSlots() ) );
        detailResponse.type( interview.getType() );
        detailResponse.updatedAt( interview.getUpdatedAt() );

        return detailResponse.build();
    }

    @Override
    public InterviewDTO.SummaryResponse toSummaryResponse(Interview interview) {
        if ( interview == null ) {
            return null;
        }

        InterviewDTO.SummaryResponse.SummaryResponseBuilder summaryResponse = InterviewDTO.SummaryResponse.builder();

        summaryResponse.scheduleDate( interview.getScheduledAt() );
        summaryResponse.currentStatus( interview.getStatus() );
        summaryResponse.applicationId( interview.getApplicationId() );
        summaryResponse.applicationReference( interview.getApplicationReference() );
        summaryResponse.candidateName( interview.getCandidateName() );
        summaryResponse.format( interview.getFormat() );
        summaryResponse.id( interview.getId() );
        summaryResponse.isRecommended( interview.getIsRecommended() );
        summaryResponse.jobTitle( interview.getJobTitle() );
        summaryResponse.location( interview.getLocation() );
        summaryResponse.type( interview.getType() );

        return summaryResponse.build();
    }

    @Override
    public List<InterviewDTO.SummaryResponse> toSummaryResponseList(List<Interview> interviews) {
        if ( interviews == null ) {
            return null;
        }

        List<InterviewDTO.SummaryResponse> list = new ArrayList<InterviewDTO.SummaryResponse>( interviews.size() );
        for ( Interview interview : interviews ) {
            list.add( toSummaryResponse( interview ) );
        }

        return list;
    }

    @Override
    public Interview fromEvent(InterviewRequestedEvent event) {
        if ( event == null ) {
            return null;
        }

        Interview.InterviewBuilder<?, ?> interview = Interview.builder();

        interview.applicationId( event.getApplicationId() );
        interview.applicationReference( event.getApplicationReference() );
        interview.candidateId( event.getCandidateId() );
        interview.candidateName( event.getCandidateName() );
        interview.jobDepartment( event.getJobDepartment() );
        interview.jobPostingId( event.getJobPostingId() );
        interview.jobTitle( event.getJobTitle() );

        interview.status( Interview.InterviewStatus.REQUESTED );
        interview.type( Interview.InterviewType.TECHNICAL );
        interview.deleted( false );
        interview.createdAt( java.time.LocalDateTime.now() );

        return interview.build();
    }

    @Override
    public void updateFromDto(InterviewDTO.UpdateRequest dto, Interview interview) {
        if ( dto == null ) {
            return;
        }

        if ( dto.getDescription() != null ) {
            interview.setDescription( dto.getDescription() );
        }
        if ( dto.getLocation() != null ) {
            interview.setLocation( dto.getLocation() );
        }
        if ( dto.getMeetingLink() != null ) {
            interview.setMeetingLink( dto.getMeetingLink() );
        }
        if ( dto.getType() != null ) {
            interview.setType( dto.getType() );
        }
    }

    protected List<FeedbackDTO.FeedbackResponse> interviewFeedbackSetToFeedbackResponseList(Set<InterviewFeedback> set) {
        if ( set == null ) {
            return null;
        }

        List<FeedbackDTO.FeedbackResponse> list = new ArrayList<FeedbackDTO.FeedbackResponse>( set.size() );
        for ( InterviewFeedback interviewFeedback : set ) {
            list.add( feedbackMapper.toResponse( interviewFeedback ) );
        }

        return list;
    }

    protected List<ParticipantDTO.ParticipantResponse> interviewParticipantSetToParticipantResponseList(Set<InterviewParticipant> set) {
        if ( set == null ) {
            return null;
        }

        List<ParticipantDTO.ParticipantResponse> list = new ArrayList<ParticipantDTO.ParticipantResponse>( set.size() );
        for ( InterviewParticipant interviewParticipant : set ) {
            list.add( participantMapper.toResponse( interviewParticipant ) );
        }

        return list;
    }

    protected List<InterviewSlotDTO.SlotResponse> interviewSlotSetToSlotResponseList(Set<InterviewSlot> set) {
        if ( set == null ) {
            return null;
        }

        List<InterviewSlotDTO.SlotResponse> list = new ArrayList<InterviewSlotDTO.SlotResponse>( set.size() );
        for ( InterviewSlot interviewSlot : set ) {
            list.add( interviewSlotMapper.toResponse( interviewSlot ) );
        }

        return list;
    }
}
