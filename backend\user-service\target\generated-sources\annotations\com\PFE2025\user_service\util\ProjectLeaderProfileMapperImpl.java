package com.PFE2025.user_service.util;

import com.PFE2025.user_service.dto.request.ProjectLeaderCreateRequest;
import com.PFE2025.user_service.dto.request.ProjectLeaderUpdateRequest;
import com.PFE2025.user_service.dto.request.UserCreateRequest;
import com.PFE2025.user_service.dto.response.AuthServiceUserDTO;
import com.PFE2025.user_service.dto.response.ProjectLeaderProfileResponse;
import com.PFE2025.user_service.model.ProjectLeaderProfile;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T14:18:04+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ProjectLeaderProfileMapperImpl implements ProjectLeaderProfileMapper {

    @Override
    public ProjectLeaderProfile fromCreateRequest(ProjectLeaderCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        ProjectLeaderProfile.ProjectLeaderProfileBuilder<?, ?> projectLeaderProfile = ProjectLeaderProfile.builder();

        if ( request.getBudgetResponsibility() != null ) {
            projectLeaderProfile.budgetResponsibility( request.getBudgetResponsibility() );
        }
        List<String> list = request.getCertifications();
        if ( list != null ) {
            projectLeaderProfile.certifications( new ArrayList<String>( list ) );
        }
        if ( request.getCompletedProjects() != null ) {
            projectLeaderProfile.completedProjects( request.getCompletedProjects() );
        }
        if ( request.getCurrentProjects() != null ) {
            projectLeaderProfile.currentProjects( request.getCurrentProjects() );
        }
        if ( request.getDepartment() != null ) {
            projectLeaderProfile.department( request.getDepartment() );
        }
        if ( request.getEmail() != null ) {
            projectLeaderProfile.email( request.getEmail() );
        }
        if ( request.getFirstName() != null ) {
            projectLeaderProfile.firstName( request.getFirstName() );
        }
        if ( request.getLastName() != null ) {
            projectLeaderProfile.lastName( request.getLastName() );
        }
        if ( request.getLocation() != null ) {
            projectLeaderProfile.location( request.getLocation() );
        }
        if ( request.getManagementLevel() != null ) {
            projectLeaderProfile.managementLevel( request.getManagementLevel() );
        }
        if ( request.getPhone() != null ) {
            projectLeaderProfile.phone( request.getPhone() );
        }
        List<String> list1 = request.getProjectsManaged();
        if ( list1 != null ) {
            projectLeaderProfile.projectsManaged( new ArrayList<String>( list1 ) );
        }
        List<String> list2 = request.getSpecializations();
        if ( list2 != null ) {
            projectLeaderProfile.specializations( new ArrayList<String>( list2 ) );
        }
        if ( request.getTeamSize() != null ) {
            projectLeaderProfile.teamSize( request.getTeamSize() );
        }
        if ( request.getYearsOfExperience() != null ) {
            projectLeaderProfile.yearsOfExperience( request.getYearsOfExperience() );
        }

        projectLeaderProfile.userType( "INTERNAL" );

        return projectLeaderProfile.build();
    }

    @Override
    public void updateFromRequest(ProjectLeaderUpdateRequest request, ProjectLeaderProfile profile) {
        if ( request == null ) {
            return;
        }

        if ( request.getBudgetResponsibility() != null ) {
            profile.setBudgetResponsibility( request.getBudgetResponsibility() );
        }
        if ( profile.getCertifications() != null ) {
            List<String> list = request.getCertifications();
            if ( list != null ) {
                profile.getCertifications().clear();
                profile.getCertifications().addAll( list );
            }
        }
        else {
            List<String> list = request.getCertifications();
            if ( list != null ) {
                profile.setCertifications( new ArrayList<String>( list ) );
            }
        }
        if ( request.getCompletedProjects() != null ) {
            profile.setCompletedProjects( request.getCompletedProjects() );
        }
        if ( request.getCurrentProjects() != null ) {
            profile.setCurrentProjects( request.getCurrentProjects() );
        }
        if ( request.getDepartment() != null ) {
            profile.setDepartment( request.getDepartment() );
        }
        if ( request.getEmail() != null ) {
            profile.setEmail( request.getEmail() );
        }
        if ( request.getFirstName() != null ) {
            profile.setFirstName( request.getFirstName() );
        }
        if ( request.getLastName() != null ) {
            profile.setLastName( request.getLastName() );
        }
        if ( request.getLocation() != null ) {
            profile.setLocation( request.getLocation() );
        }
        if ( request.getManagementLevel() != null ) {
            profile.setManagementLevel( request.getManagementLevel() );
        }
        if ( request.getPhone() != null ) {
            profile.setPhone( request.getPhone() );
        }
        if ( profile.getProjectsManaged() != null ) {
            List<String> list1 = request.getProjectsManaged();
            if ( list1 != null ) {
                profile.getProjectsManaged().clear();
                profile.getProjectsManaged().addAll( list1 );
            }
        }
        else {
            List<String> list1 = request.getProjectsManaged();
            if ( list1 != null ) {
                profile.setProjectsManaged( new ArrayList<String>( list1 ) );
            }
        }
        if ( profile.getSpecializations() != null ) {
            List<String> list2 = request.getSpecializations();
            if ( list2 != null ) {
                profile.getSpecializations().clear();
                profile.getSpecializations().addAll( list2 );
            }
        }
        else {
            List<String> list2 = request.getSpecializations();
            if ( list2 != null ) {
                profile.setSpecializations( new ArrayList<String>( list2 ) );
            }
        }
        if ( request.getTeamSize() != null ) {
            profile.setTeamSize( request.getTeamSize() );
        }
        if ( request.getYearsOfExperience() != null ) {
            profile.setYearsOfExperience( request.getYearsOfExperience() );
        }

        updateFullName( profile );
    }

    @Override
    public ProjectLeaderProfileResponse toResponse(ProjectLeaderProfile profile) {
        if ( profile == null ) {
            return null;
        }

        ProjectLeaderProfileResponse.ProjectLeaderProfileResponseBuilder projectLeaderProfileResponse = ProjectLeaderProfileResponse.builder();

        if ( profile.getBudgetResponsibility() != null ) {
            projectLeaderProfileResponse.budgetResponsibility( profile.getBudgetResponsibility() );
        }
        List<String> list = profile.getCertifications();
        if ( list != null ) {
            projectLeaderProfileResponse.certifications( new ArrayList<String>( list ) );
        }
        if ( profile.getCompletedProjects() != null ) {
            projectLeaderProfileResponse.completedProjects( profile.getCompletedProjects() );
        }
        if ( profile.getCreatedAt() != null ) {
            projectLeaderProfileResponse.createdAt( profile.getCreatedAt() );
        }
        if ( profile.getCurrentProjects() != null ) {
            projectLeaderProfileResponse.currentProjects( profile.getCurrentProjects() );
        }
        if ( profile.getDepartment() != null ) {
            projectLeaderProfileResponse.department( profile.getDepartment() );
        }
        if ( profile.getEmail() != null ) {
            projectLeaderProfileResponse.email( profile.getEmail() );
        }
        if ( profile.getFirstName() != null ) {
            projectLeaderProfileResponse.firstName( profile.getFirstName() );
        }
        if ( profile.getFullName() != null ) {
            projectLeaderProfileResponse.fullName( profile.getFullName() );
        }
        if ( profile.getId() != null ) {
            projectLeaderProfileResponse.id( profile.getId() );
        }
        if ( profile.getKeycloakId() != null ) {
            projectLeaderProfileResponse.keycloakId( profile.getKeycloakId() );
        }
        if ( profile.getLastName() != null ) {
            projectLeaderProfileResponse.lastName( profile.getLastName() );
        }
        if ( profile.getLocation() != null ) {
            projectLeaderProfileResponse.location( profile.getLocation() );
        }
        if ( profile.getManagementLevel() != null ) {
            projectLeaderProfileResponse.managementLevel( profile.getManagementLevel() );
        }
        if ( profile.getPhone() != null ) {
            projectLeaderProfileResponse.phone( profile.getPhone() );
        }
        List<String> list1 = profile.getProjectsManaged();
        if ( list1 != null ) {
            projectLeaderProfileResponse.projectsManaged( new ArrayList<String>( list1 ) );
        }
        List<String> list2 = profile.getRoles();
        if ( list2 != null ) {
            projectLeaderProfileResponse.roles( new ArrayList<String>( list2 ) );
        }
        List<String> list3 = profile.getSpecializations();
        if ( list3 != null ) {
            projectLeaderProfileResponse.specializations( new ArrayList<String>( list3 ) );
        }
        if ( profile.getStatus() != null ) {
            projectLeaderProfileResponse.status( profile.getStatus() );
        }
        if ( profile.getTeamSize() != null ) {
            projectLeaderProfileResponse.teamSize( profile.getTeamSize() );
        }
        if ( profile.getUpdatedAt() != null ) {
            projectLeaderProfileResponse.updatedAt( profile.getUpdatedAt() );
        }
        if ( profile.getUserType() != null ) {
            projectLeaderProfileResponse.userType( profile.getUserType() );
        }
        if ( profile.getYearsOfExperience() != null ) {
            projectLeaderProfileResponse.yearsOfExperience( profile.getYearsOfExperience() );
        }

        projectLeaderProfileResponse.enriched( profile.isEnriched() );

        return projectLeaderProfileResponse.build();
    }

    @Override
    public ProjectLeaderProfileResponse enrichWithAuthData(ProjectLeaderProfile profile, AuthServiceUserDTO authUser) {
        if ( profile == null && authUser == null ) {
            return null;
        }

        ProjectLeaderProfileResponse.ProjectLeaderProfileResponseBuilder projectLeaderProfileResponse = ProjectLeaderProfileResponse.builder();

        if ( profile != null ) {
            if ( profile.getBudgetResponsibility() != null ) {
                projectLeaderProfileResponse.budgetResponsibility( profile.getBudgetResponsibility() );
            }
            List<String> list1 = profile.getCertifications();
            if ( list1 != null ) {
                projectLeaderProfileResponse.certifications( new ArrayList<String>( list1 ) );
            }
            if ( profile.getCompletedProjects() != null ) {
                projectLeaderProfileResponse.completedProjects( profile.getCompletedProjects() );
            }
            if ( profile.getCreatedAt() != null ) {
                projectLeaderProfileResponse.createdAt( profile.getCreatedAt() );
            }
            if ( profile.getCurrentProjects() != null ) {
                projectLeaderProfileResponse.currentProjects( profile.getCurrentProjects() );
            }
            if ( profile.getDepartment() != null ) {
                projectLeaderProfileResponse.department( profile.getDepartment() );
            }
            projectLeaderProfileResponse.enriched( profile.isEnriched() );
            if ( profile.getId() != null ) {
                projectLeaderProfileResponse.id( profile.getId() );
            }
            if ( profile.getKeycloakId() != null ) {
                projectLeaderProfileResponse.keycloakId( profile.getKeycloakId() );
            }
            if ( profile.getLocation() != null ) {
                projectLeaderProfileResponse.location( profile.getLocation() );
            }
            if ( profile.getManagementLevel() != null ) {
                projectLeaderProfileResponse.managementLevel( profile.getManagementLevel() );
            }
            if ( profile.getPhone() != null ) {
                projectLeaderProfileResponse.phone( profile.getPhone() );
            }
            List<String> list2 = profile.getProjectsManaged();
            if ( list2 != null ) {
                projectLeaderProfileResponse.projectsManaged( new ArrayList<String>( list2 ) );
            }
            List<String> list3 = profile.getSpecializations();
            if ( list3 != null ) {
                projectLeaderProfileResponse.specializations( new ArrayList<String>( list3 ) );
            }
            if ( profile.getTeamSize() != null ) {
                projectLeaderProfileResponse.teamSize( profile.getTeamSize() );
            }
            if ( profile.getUpdatedAt() != null ) {
                projectLeaderProfileResponse.updatedAt( profile.getUpdatedAt() );
            }
            if ( profile.getUserType() != null ) {
                projectLeaderProfileResponse.userType( profile.getUserType() );
            }
            if ( profile.getYearsOfExperience() != null ) {
                projectLeaderProfileResponse.yearsOfExperience( profile.getYearsOfExperience() );
            }
        }
        if ( authUser != null ) {
            List<String> list = setToList( authUser.getRoles() );
            if ( list != null ) {
                projectLeaderProfileResponse.roles( list );
            }
            if ( authUser.getFirstName() != null ) {
                projectLeaderProfileResponse.firstName( authUser.getFirstName() );
            }
            if ( authUser.getLastName() != null ) {
                projectLeaderProfileResponse.lastName( authUser.getLastName() );
            }
            if ( authUser.getEmail() != null ) {
                projectLeaderProfileResponse.email( authUser.getEmail() );
            }
            if ( authUser.getFullName() != null ) {
                projectLeaderProfileResponse.fullName( authUser.getFullName() );
            }
        }
        projectLeaderProfileResponse.status( authUser.isEnabled() ? "ACTIVE" : "INACTIVE" );

        return projectLeaderProfileResponse.build();
    }

    @Override
    public UserCreateRequest toUserCreateRequest(ProjectLeaderCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        UserCreateRequest.UserCreateRequestBuilder userCreateRequest = UserCreateRequest.builder();

        if ( request.getDepartment() != null ) {
            userCreateRequest.department( request.getDepartment() );
        }
        if ( request.getEmail() != null ) {
            userCreateRequest.email( request.getEmail() );
        }
        if ( request.getEmailVerified() != null ) {
            userCreateRequest.emailVerified( request.getEmailVerified() );
        }
        if ( request.getEnabled() != null ) {
            userCreateRequest.enabled( request.getEnabled() );
        }
        if ( request.getFirstName() != null ) {
            userCreateRequest.firstName( request.getFirstName() );
        }
        if ( request.getLastName() != null ) {
            userCreateRequest.lastName( request.getLastName() );
        }
        if ( request.getPassword() != null ) {
            userCreateRequest.password( request.getPassword() );
        }
        if ( request.getPhone() != null ) {
            userCreateRequest.phone( request.getPhone() );
        }
        if ( request.getUsername() != null ) {
            userCreateRequest.username( request.getUsername() );
        }

        userCreateRequest.roles( java.util.Set.of("PROJECT_LEADER") );
        userCreateRequest.userType( "INTERNAL" );

        return userCreateRequest.build();
    }
}
