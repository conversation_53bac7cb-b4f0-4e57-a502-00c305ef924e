{"t":{"$date":"2025-04-13T15:44:46.238Z"},"s":"I","c":"<PERSON><PERSON>GOSH","id":1000000005,"ctx":"config","msg":"User updated"}
{"t":{"$date":"2025-04-13T15:44:46.245Z"},"s":"I","c":"MONGOSH","id":1000000048,"ctx":"config","msg":"Loading global configuration file","attr":{"filename":"/etc/mongosh.conf","found":false}}
{"t":{"$date":"2025-04-13T15:44:46.245Z"},"s":"I","c":"MONGOSH","id":1000000000,"ctx":"log","msg":"Starting log","attr":{"execPath":"/usr/bin/mongosh","envInfo":{"EDITOR":null,"NODE_OPTIONS":null,"TERM":null},"version":"2.4.0","distributionKind":"compiled","buildArch":"x64","buildPlatform":"linux","buildTarget":"linux-x64","buildTime":"2025-02-20T14:52:12.833Z","gitVersion":"df27287829c8ead52766ea931f85dd2a9928e8a2","nodeVersion":"v20.18.3","opensslVersion":"3.0.15+quic","sharedOpenssl":false,"runtimeArch":"x64","runtimePlatform":"linux","runtimeGlibcVersion":"2.39","deps":{"nodeDriverVersion":"6.13.0","libmongocryptVersion":"1.11.0","libmongocryptNodeBindingsVersion":"6.1.1","kerberosVersion":"2.1.0"}}}
{"t":{"$date":"2025-04-13T15:44:46.378Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000049,"ctx":"mongosh-connect","msg":"Loaded system CA list","attr":{"caCount":298,"asyncFallbackError":null,"systemCertsError":null,"messages":[]}}
{"t":{"$date":"2025-04-13T15:44:46.403Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000042,"ctx":"mongosh-connect","msg":"Initiating connection attempt","attr":{"uri":"mongodb://127.0.0.1:27017/admin?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh****.0","driver":{"name":"nodejs|mongosh","version":"6.13.0|2.4.0"},"devtoolsConnectVersion":"3.4.1","host":"127.0.0.1:27017"}}
{"t":{"$date":"2025-04-13T15:44:46.412Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000035,"ctx":"mongosh-connect","msg":"Server heartbeat succeeded","attr":{"connectionId":"127.0.0.1:27017"}}
{"t":{"$date":"2025-04-13T15:44:46.480Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000037,"ctx":"mongosh-connect","msg":"Connection attempt finished"}
{"t":{"$date":"2025-04-13T15:44:46.500Z"},"s":"I","c":"MONGOSH","id":1000000010,"ctx":"shell-api","msg":"Initialized context","attr":{"method":"setCtx","arguments":{}}}
{"t":{"$date":"2025-04-13T15:44:46.502Z"},"s":"I","c":"MONGOSH-SNIPPETS","id":1000000024,"ctx":"snippets","msg":"Fetching snippet index","attr":{"refreshMode":"allow-cached"}}
{"t":{"$date":"2025-04-13T15:44:46.502Z"},"s":"I","c":"MONGOSH-SNIPPETS","id":1000000019,"ctx":"snippets","msg":"Loaded snippets","attr":{"installdir":"/data/db/.mongodb/mongosh/snippets"}}
{"t":{"$date":"2025-04-13T15:44:46.505Z"},"s":"I","c":"MONGOSH-SNIPPETS","id":1000000028,"ctx":"snippets","msg":"Modifying snippets package.json failed","attr":{"error":"ENOENT: no such file or directory, open '/data/db/.mongodb/mongosh/snippets/package.json'"}}
{"t":{"$date":"2025-04-13T15:44:46.710Z"},"s":"I","c":"MONGOSH","id":1000000004,"ctx":"connect","msg":"Connecting to server","attr":{"userId":null,"telemetryAnonymousId":"67fbdbedfa71acfc6b51e942","connectionUri":"mongodb://<ip address>:27017/admin?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh****.0","mongosh_version":"2.4.0","session_id":"67fbdbeeb58432bcfa51e943","is_localhost":true,"is_do_url":false,"is_atlas_url":false,"is_atlas":false,"server_version":"8.0.5","node_version":"v20.18.3","server_os":"linux","server_arch":"x86_64","is_enterprise":false,"auth_type":null,"is_data_federation":false,"is_stream":false,"dl_version":null,"atlas_version":null,"is_genuine":true,"non_genuine_server_name":"mongodb","is_local_atlas":false,"fcv":"8.0","api_version":null,"api_strict":null,"api_deprecation_errors":null,"atlas_hostname":null}}
{"t":{"$date":"2025-04-13T15:44:46.714Z"},"s":"I","c":"MONGOSH","id":1000000002,"ctx":"repl","msg":"Started REPL","attr":{"version":"2.4.0"}}
{"t":{"$date":"2025-04-13T15:44:46.912Z"},"s":"I","c":"MONGOSH","id":1000000011,"ctx":"shell-api","msg":"Performed API call","attr":{"method":"createUser","class":"Database","db":"admin","arguments":{}}}
{"t":{"$date":"2025-04-13T15:44:47.081Z"},"s":"I","c":"MONGOSH","id":1000000045,"ctx":"analytics","msg":"Flushed outstanding data","attr":{"flushError":"Trying to persist throttle state before userId is set","flushDuration":0}}
