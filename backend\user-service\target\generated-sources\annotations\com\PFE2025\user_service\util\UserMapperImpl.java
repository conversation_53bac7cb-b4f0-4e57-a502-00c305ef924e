package com.PFE2025.user_service.util;

import com.PFE2025.user_service.dto.request.CandidateRegistrationRequest;
import com.PFE2025.user_service.dto.request.UserCreateRequest;
import com.PFE2025.user_service.dto.request.UserUpdateRequest;
import com.PFE2025.user_service.dto.response.UserDTO;
import com.PFE2025.user_service.dto.response.UserRegistrationResponse;
import com.PFE2025.user_service.model.User;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashSet;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T02:02:01+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class UserMapperImpl implements UserMapper {

    @Override
    public User localUserFromCreateRequest(UserCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        User.UserBuilder<?, ?> user = User.builder();

        user.department( request.getDepartment() );
        user.phone( request.getPhone() );

        return user.build();
    }

    @Override
    public User localUserFromRegistrationRequest(CandidateRegistrationRequest request) {
        if ( request == null ) {
            return null;
        }

        User.UserBuilder<?, ?> user = User.builder();

        user.phone( request.getPhone() );

        user.department( "N/A" );

        return user.build();
    }

    @Override
    public void updateLocalUserFromRequest(UserUpdateRequest request, User user) {
        if ( request == null ) {
            return;
        }

        if ( request.getDepartment() != null ) {
            user.setDepartment( request.getDepartment() );
        }
        if ( request.getPhone() != null ) {
            user.setPhone( request.getPhone() );
        }
    }

    @Override
    public UserDTO mapLocalUserToUserDTO(User user) {
        if ( user == null ) {
            return null;
        }

        UserDTO.UserDTOBuilder userDTO = UserDTO.builder();

        userDTO.createdAt( user.getCreatedAt() );
        userDTO.department( user.getDepartment() );
        userDTO.id( user.getId() );
        userDTO.keycloakId( user.getKeycloakId() );
        userDTO.phone( user.getPhone() );
        userDTO.updatedAt( user.getUpdatedAt() );

        userDTO.userType( user.getUserType().name() );

        return userDTO.build();
    }

    @Override
    public UserDTO toUserDTO(User user) {
        if ( user == null ) {
            return null;
        }

        UserDTO.UserDTOBuilder userDTO = UserDTO.builder();

        userDTO.createdAt( user.getCreatedAt() );
        userDTO.department( user.getDepartment() );
        userDTO.id( user.getId() );
        userDTO.keycloakId( user.getKeycloakId() );
        userDTO.phone( user.getPhone() );
        userDTO.updatedAt( user.getUpdatedAt() );

        userDTO.userType( user.getUserType().name() );

        return userDTO.build();
    }

    @Override
    public UserRegistrationResponse toUserRegistrationResponse(User user) {
        if ( user == null ) {
            return null;
        }

        UserRegistrationResponse.UserRegistrationResponseBuilder userRegistrationResponse = UserRegistrationResponse.builder();

        userRegistrationResponse.createdAt( user.getCreatedAt() );
        userRegistrationResponse.id( user.getId() );
        userRegistrationResponse.keycloakId( user.getKeycloakId() );
        userRegistrationResponse.phone( user.getPhone() );
        userRegistrationResponse.updatedAt( user.getUpdatedAt() );
        if ( user.getUserType() != null ) {
            userRegistrationResponse.userType( user.getUserType().name() );
        }

        return userRegistrationResponse.build();
    }

    @Override
    public UserCreateRequest userCreateRequestFromRegistration(CandidateRegistrationRequest request) {
        if ( request == null ) {
            return null;
        }

        UserCreateRequest.UserCreateRequestBuilder userCreateRequest = UserCreateRequest.builder();

        userCreateRequest.email( request.getEmail() );
        userCreateRequest.firstName( request.getFirstName() );
        userCreateRequest.lastName( request.getLastName() );
        userCreateRequest.password( request.getPassword() );
        userCreateRequest.phone( request.getPhone() );
        userCreateRequest.username( request.getUsername() );

        userCreateRequest.roles( request.getRoles() != null ? request.getRoles() : new HashSet<>() );
        userCreateRequest.enabled( true );
        userCreateRequest.emailVerified( true );

        return userCreateRequest.build();
    }
}
