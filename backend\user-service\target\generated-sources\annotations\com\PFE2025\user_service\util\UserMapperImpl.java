package com.PFE2025.user_service.util;

import com.PFE2025.user_service.dto.request.CandidateRegistrationRequest;
import com.PFE2025.user_service.dto.request.UserCreateRequest;
import com.PFE2025.user_service.dto.request.UserUpdateRequest;
import com.PFE2025.user_service.dto.response.UserDTO;
import com.PFE2025.user_service.dto.response.UserRegistrationResponse;
import com.PFE2025.user_service.model.User;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashSet;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T14:39:57+0100",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.8 (Oracle Corporation)"
)
@Component
public class UserMapperImpl implements UserMapper {

    @Override
    public User localUserFromCreateRequest(UserCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        User.UserBuilder<?, ?> user = User.builder();

        user.phone( request.getPhone() );
        user.department( request.getDepartment() );

        return user.build();
    }

    @Override
    public User localUserFromRegistrationRequest(CandidateRegistrationRequest request) {
        if ( request == null ) {
            return null;
        }

        User.UserBuilder<?, ?> user = User.builder();

        user.phone( request.getPhone() );

        user.department( "N/A" );

        return user.build();
    }

    @Override
    public void updateLocalUserFromRequest(UserUpdateRequest request, User user) {
        if ( request == null ) {
            return;
        }

        if ( request.getPhone() != null ) {
            user.setPhone( request.getPhone() );
        }
        if ( request.getDepartment() != null ) {
            user.setDepartment( request.getDepartment() );
        }
    }

    @Override
    public UserDTO mapLocalUserToUserDTO(User user) {
        if ( user == null ) {
            return null;
        }

        UserDTO.UserDTOBuilder userDTO = UserDTO.builder();

        userDTO.id( user.getId() );
        userDTO.keycloakId( user.getKeycloakId() );
        userDTO.phone( user.getPhone() );
        userDTO.department( user.getDepartment() );
        userDTO.createdAt( user.getCreatedAt() );
        userDTO.updatedAt( user.getUpdatedAt() );

        userDTO.userType( user.getUserType().name() );

        return userDTO.build();
    }

    @Override
    public UserDTO toUserDTO(User user) {
        if ( user == null ) {
            return null;
        }

        UserDTO.UserDTOBuilder userDTO = UserDTO.builder();

        userDTO.id( user.getId() );
        userDTO.keycloakId( user.getKeycloakId() );
        userDTO.phone( user.getPhone() );
        userDTO.department( user.getDepartment() );
        userDTO.createdAt( user.getCreatedAt() );
        userDTO.updatedAt( user.getUpdatedAt() );

        userDTO.userType( user.getUserType().name() );

        return userDTO.build();
    }

    @Override
    public UserRegistrationResponse toUserRegistrationResponse(User user) {
        if ( user == null ) {
            return null;
        }

        UserRegistrationResponse.UserRegistrationResponseBuilder userRegistrationResponse = UserRegistrationResponse.builder();

        userRegistrationResponse.id( user.getId() );
        userRegistrationResponse.keycloakId( user.getKeycloakId() );
        userRegistrationResponse.phone( user.getPhone() );
        if ( user.getUserType() != null ) {
            userRegistrationResponse.userType( user.getUserType().name() );
        }
        userRegistrationResponse.createdAt( user.getCreatedAt() );
        userRegistrationResponse.updatedAt( user.getUpdatedAt() );

        return userRegistrationResponse.build();
    }

    @Override
    public UserCreateRequest userCreateRequestFromRegistration(CandidateRegistrationRequest request) {
        if ( request == null ) {
            return null;
        }

        UserCreateRequest.UserCreateRequestBuilder userCreateRequest = UserCreateRequest.builder();

        userCreateRequest.username( request.getUsername() );
        userCreateRequest.firstName( request.getFirstName() );
        userCreateRequest.lastName( request.getLastName() );
        userCreateRequest.email( request.getEmail() );
        userCreateRequest.password( request.getPassword() );
        userCreateRequest.phone( request.getPhone() );

        userCreateRequest.roles( request.getRoles() != null ? request.getRoles() : new HashSet<>() );
        userCreateRequest.enabled( true );
        userCreateRequest.emailVerified( true );

        return userCreateRequest.build();
    }
}
