package com.PFE2025.user_service.model;

import lombok.*;
import lombok.experimental.SuperBuilder;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * Entité représentant le profil complet d'un CEO.
 * Contient les données de base de l'utilisateur plus des champs spécifiques au rôle.
 * Utilisée uniquement pour les utilisateurs de type CEO.
 * 
 * NOTE: Le CEO n'a pas de département car il supervise toute l'organisation.
 */
@Document(collection = "ceo_profiles")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class CeoProfile extends BaseEntity {

    @Id
    private String id;

    @Indexed(unique = true)
    private String keycloakId;

    // === DONNÉES DE BASE (comme dans GET /profile/me) ===
    private String firstName;
    private String lastName;
    private String fullName;
    @Indexed
    private String email;
    private List<String> roles;
    private String userType;
    private String status;
    private String location;
    private String phone;

    // === CHAMPS SPÉCIFIQUES CEO ===
    // PAS DE DÉPARTEMENT - le CEO supervise toute l'organisation
    
    private String companyName; // Nom de l'entreprise
    private Integer totalEmployees; // Nombre total d'employés sous sa responsabilité
    private List<String> strategicObjectives; // Objectifs stratégiques
    private Integer yearsAsLeader; // Années d'expérience en leadership
    private List<String> previousCompanies; // Entreprises précédentes dirigées
    private List<String> certifications; // Certifications de leadership/management
    private List<String> boardMemberships; // Participations à des conseils d'administration
    private String vision; // Vision de l'entreprise
    private List<String> keyAchievements; // Réalisations clés
    private Double annualRevenue; // Chiffre d'affaires annuel sous sa direction
    private List<String> industries; // Secteurs d'activité d'expérience

    // === MÉTHODES UTILITAIRES ===
    
    /**
     * Calcule automatiquement le fullName à partir du firstName et lastName
     */
    public void updateFullName() {
        if (firstName != null && lastName != null) {
            this.fullName = firstName + " " + lastName;
        }
    }

    /**
     * Vérifie si le profil est enrichi avec les données spécifiques
     */
    public boolean isEnriched() {
        return companyName != null && totalEmployees != null;
    }

    /**
     * Calcule le score de leadership basé sur l'expérience et les réalisations
     */
    public int calculateLeadershipScore() {
        int score = 0;
        if (yearsAsLeader != null) {
            score += Math.min(yearsAsLeader * 10, 50); // Max 50 points pour l'expérience
        }
        if (previousCompanies != null) {
            score += Math.min(previousCompanies.size() * 10, 30); // Max 30 points pour la diversité
        }
        if (keyAchievements != null) {
            score += Math.min(keyAchievements.size() * 5, 20); // Max 20 points pour les réalisations
        }
        return Math.min(score, 100);
    }
}
