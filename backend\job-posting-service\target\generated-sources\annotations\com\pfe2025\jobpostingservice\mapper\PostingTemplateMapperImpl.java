package com.pfe2025.jobpostingservice.mapper;

import com.pfe2025.jobpostingservice.dto.PostingTemplateDTO;
import com.pfe2025.jobpostingservice.model.PostingTemplate;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T14:17:57+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class PostingTemplateMapperImpl implements PostingTemplateMapper {

    @Override
    public PostingTemplateDTO.Response toResponseDto(PostingTemplate template) {
        if ( template == null ) {
            return null;
        }

        PostingTemplateDTO.Response.ResponseBuilder<?, ?> response = PostingTemplateDTO.Response.builder();

        response.createdAt( template.getCreatedAt() );
        response.createdBy( template.getCreatedBy() );
        response.department( template.getDepartment() );
        response.description( template.getDescription() );
        response.id( template.getId() );
        response.isActive( template.getIsActive() );
        response.lastModifiedAt( template.getLastModifiedAt() );
        response.name( template.getName() );
        response.structure( template.getStructure() );
        response.version( template.getVersion() );

        return response.build();
    }

    @Override
    public PostingTemplateDTO.Summary toSummaryDto(PostingTemplate template) {
        if ( template == null ) {
            return null;
        }

        PostingTemplateDTO.Summary.SummaryBuilder<?, ?> summary = PostingTemplateDTO.Summary.builder();

        summary.id( template.getId() );
        summary.name( template.getName() );
        summary.description( template.getDescription() );
        summary.department( template.getDepartment() );
        summary.isActive( template.getIsActive() );

        return summary.build();
    }

    @Override
    public PostingTemplate toEntity(PostingTemplateDTO.Request dto) {
        if ( dto == null ) {
            return null;
        }

        PostingTemplate.PostingTemplateBuilder<?, ?> postingTemplate = PostingTemplate.builder();

        postingTemplate.department( dto.getDepartment() );
        postingTemplate.description( dto.getDescription() );
        postingTemplate.isActive( dto.getIsActive() );
        postingTemplate.name( dto.getName() );
        postingTemplate.structure( dto.getStructure() );

        return postingTemplate.build();
    }

    @Override
    public void updateFromDto(PostingTemplateDTO.Request dto, PostingTemplate template) {
        if ( dto == null ) {
            return;
        }

        template.setDepartment( dto.getDepartment() );
        template.setDescription( dto.getDescription() );
        template.setIsActive( dto.getIsActive() );
        template.setName( dto.getName() );
        template.setStructure( dto.getStructure() );
    }

    @Override
    public List<PostingTemplateDTO.Response> toResponseDtoList(List<PostingTemplate> templates) {
        if ( templates == null ) {
            return null;
        }

        List<PostingTemplateDTO.Response> list = new ArrayList<PostingTemplateDTO.Response>( templates.size() );
        for ( PostingTemplate postingTemplate : templates ) {
            list.add( toResponseDto( postingTemplate ) );
        }

        return list;
    }

    @Override
    public List<PostingTemplateDTO.Summary> toSummaryDtoList(List<PostingTemplate> templates) {
        if ( templates == null ) {
            return null;
        }

        List<PostingTemplateDTO.Summary> list = new ArrayList<PostingTemplateDTO.Summary>( templates.size() );
        for ( PostingTemplate postingTemplate : templates ) {
            list.add( toSummaryDto( postingTemplate ) );
        }

        return list;
    }
}
