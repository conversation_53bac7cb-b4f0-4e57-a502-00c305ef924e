package com.pfe2025.jobpostingservice.mapper;

import com.pfe2025.jobpostingservice.dto.ContentFragmentDTO;
import com.pfe2025.jobpostingservice.model.ContentFragment;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T02:01:50+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ContentFragmentMapperImpl implements ContentFragmentMapper {

    @Override
    public ContentFragmentDTO.Response toResponseDto(ContentFragment fragment) {
        if ( fragment == null ) {
            return null;
        }

        ContentFragmentDTO.Response.ResponseBuilder response = ContentFragmentDTO.Response.builder();

        response.content( fragment.getContent() );
        response.createdAt( fragment.getCreatedAt() );
        response.createdBy( fragment.getCreatedBy() );
        response.fragmentKey( fragment.getFragmentKey() );
        response.id( fragment.getId() );
        response.isActive( fragment.getIsActive() );
        response.language( fragment.getLanguage() );
        response.lastModifiedAt( fragment.getLastModifiedAt() );
        response.lastModifiedBy( fragment.getLastModifiedBy() );
        response.type( fragment.getType() );

        return response.build();
    }

    @Override
    public ContentFragmentDTO.Summary toSummaryDto(ContentFragment fragment) {
        if ( fragment == null ) {
            return null;
        }

        ContentFragmentDTO.Summary.SummaryBuilder summary = ContentFragmentDTO.Summary.builder();

        summary.id( fragment.getId() );
        summary.fragmentKey( fragment.getFragmentKey() );
        summary.type( fragment.getType() );
        summary.language( fragment.getLanguage() );
        summary.isActive( fragment.getIsActive() );

        return summary.build();
    }

    @Override
    public ContentFragment toEntity(ContentFragmentDTO.Request dto) {
        if ( dto == null ) {
            return null;
        }

        ContentFragment.ContentFragmentBuilder<?, ?> contentFragment = ContentFragment.builder();

        contentFragment.content( dto.getContent() );
        contentFragment.fragmentKey( dto.getFragmentKey() );
        contentFragment.isActive( dto.getIsActive() );
        contentFragment.language( dto.getLanguage() );
        contentFragment.type( dto.getType() );

        return contentFragment.build();
    }

    @Override
    public void updateFromDto(ContentFragmentDTO.Request dto, ContentFragment fragment) {
        if ( dto == null ) {
            return;
        }

        fragment.setContent( dto.getContent() );
        fragment.setFragmentKey( dto.getFragmentKey() );
        fragment.setIsActive( dto.getIsActive() );
        fragment.setLanguage( dto.getLanguage() );
        fragment.setType( dto.getType() );
    }

    @Override
    public List<ContentFragmentDTO.Response> toResponseDtoList(List<ContentFragment> fragments) {
        if ( fragments == null ) {
            return null;
        }

        List<ContentFragmentDTO.Response> list = new ArrayList<ContentFragmentDTO.Response>( fragments.size() );
        for ( ContentFragment contentFragment : fragments ) {
            list.add( toResponseDto( contentFragment ) );
        }

        return list;
    }

    @Override
    public List<ContentFragmentDTO.Summary> toSummaryDtoList(List<ContentFragment> fragments) {
        if ( fragments == null ) {
            return null;
        }

        List<ContentFragmentDTO.Summary> list = new ArrayList<ContentFragmentDTO.Summary>( fragments.size() );
        for ( ContentFragment contentFragment : fragments ) {
            list.add( toSummaryDto( contentFragment ) );
        }

        return list;
    }
}
