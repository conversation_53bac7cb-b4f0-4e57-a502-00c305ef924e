package com.pfe2025.jobpostingservice.mapper;

import com.pfe2025.jobpostingservice.dto.JobPostingSkillDTO;
import com.pfe2025.jobpostingservice.model.JobPostingSkill;
import java.util.LinkedHashSet;
import java.util.Set;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T14:17:57+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class JobPostingSkillMapperImpl implements JobPostingSkillMapper {

    @Override
    public JobPostingSkillDTO toDto(JobPostingSkill skill) {
        if ( skill == null ) {
            return null;
        }

        JobPostingSkillDTO.JobPostingSkillDTOBuilder<?, ?> jobPostingSkillDTO = JobPostingSkillDTO.builder();

        jobPostingSkillDTO.description( skill.getDescription() );
        jobPostingSkillDTO.id( skill.getId() );
        jobPostingSkillDTO.isRequired( skill.getIsRequired() );
        jobPostingSkillDTO.level( skill.getLevel() );
        jobPostingSkillDTO.name( skill.getName() );

        return jobPostingSkillDTO.build();
    }

    @Override
    public JobPostingSkill toEntity(JobPostingSkillDTO dto) {
        if ( dto == null ) {
            return null;
        }

        JobPostingSkill.JobPostingSkillBuilder<?, ?> jobPostingSkill = JobPostingSkill.builder();

        jobPostingSkill.description( dto.getDescription() );
        jobPostingSkill.isRequired( dto.getIsRequired() );
        jobPostingSkill.level( dto.getLevel() );
        jobPostingSkill.name( dto.getName() );

        return jobPostingSkill.build();
    }

    @Override
    public Set<JobPostingSkillDTO> toDtoSet(Set<JobPostingSkill> skills) {
        if ( skills == null ) {
            return null;
        }

        Set<JobPostingSkillDTO> set = new LinkedHashSet<JobPostingSkillDTO>( Math.max( (int) ( skills.size() / .75f ) + 1, 16 ) );
        for ( JobPostingSkill jobPostingSkill : skills ) {
            set.add( toDto( jobPostingSkill ) );
        }

        return set;
    }
}
