package com.pfe2025.jobpostingservice.mapper;

import com.pfe2025.jobpostingservice.dto.ActivityLogDTO;
import com.pfe2025.jobpostingservice.model.ActivityLog;
import com.pfe2025.jobpostingservice.model.JobPost;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T14:17:57+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ActivityLogMapperImpl implements ActivityLogMapper {

    @Override
    public ActivityLogDTO toDto(ActivityLog log) {
        if ( log == null ) {
            return null;
        }

        ActivityLogDTO.ActivityLogDTOBuilder<?, ?> activityLogDTO = ActivityLogDTO.builder();

        activityLogDTO.jobPostId( logJobPostId( log ) );
        activityLogDTO.action( log.getAction() );
        activityLogDTO.details( log.getDetails() );
        activityLogDTO.id( log.getId() );
        activityLogDTO.timestamp( log.getTimestamp() );
        activityLogDTO.userId( log.getUserId() );
        activityLogDTO.userName( log.getUserName() );

        return activityLogDTO.build();
    }

    private Long logJobPostId(ActivityLog activityLog) {
        if ( activityLog == null ) {
            return null;
        }
        JobPost jobPost = activityLog.getJobPost();
        if ( jobPost == null ) {
            return null;
        }
        Long id = jobPost.getId();
        if ( id == null ) {
            return null;
        }
        return id;
    }
}
