package com.PFE2025.user_service.integration;

import com.PFE2025.user_service.dto.request.CeoCreateRequest;
import com.PFE2025.user_service.dto.request.CeoUpdateRequest;
import com.PFE2025.user_service.dto.response.CeoProfileResponse;
import com.PFE2025.user_service.model.CeoProfile;
import com.PFE2025.user_service.repository.CeoProfileRepository;
import com.PFE2025.user_service.service.CeoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests d'intégration pour la gestion des profils CEO
 */
@SpringBootTest
@TestPropertySource(properties = {
    "spring.data.mongodb.database=test_user_service",
    "eureka.client.enabled=false",
    "spring.cloud.stream.bindings.processCvParsedEvent-in-0.destination=test-ai-results"
})
@Transactional
class CeoProfileIntegrationTest {

    @Autowired
    private CeoService ceoService;

    @Autowired
    private CeoProfileRepository ceoProfileRepository;

    private CeoCreateRequest createRequest;
    private String testKeycloakId;

    @BeforeEach
    void setUp() {
        // Nettoyer la base de test
        ceoProfileRepository.deleteAll();
        
        testKeycloakId = "test-keycloak-id-" + System.currentTimeMillis();
        
        createRequest = CeoCreateRequest.builder()
                .firstName("John")
                .lastName("Doe")
                .email("<EMAIL>")
                .phone("+33123456789")
                .location("Paris, France")
                .companyName("Tech Corp")
                .industries(Arrays.asList("Technology"))
                .yearsAsLeader(10)
                .totalEmployees(500)
                .password("SecurePass123!")
                .build();
    }

    @Test
    void testCreateCeoProfile() {
        // When
        CeoProfileResponse response = ceoService.createCeoProfile(createRequest, testKeycloakId);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getKeycloakId()).isEqualTo(testKeycloakId);
        assertThat(response.getFirstName()).isEqualTo("John");
        assertThat(response.getLastName()).isEqualTo("Doe");
        assertThat(response.getEmail()).isEqualTo("<EMAIL>");
        assertThat(response.getCompanyName()).isEqualTo("Tech Corp");
        assertThat(response.getRoles()).contains("CEO");
    }

    @Test
    void testGetAllCeos() {
        // Given
        ceoService.createCeoProfile(createRequest, testKeycloakId);
        
        CeoCreateRequest secondRequest = CeoCreateRequest.builder()
                .firstName("Jane")
                .lastName("Smith")
                .email("<EMAIL>")
                .phone("+33987654321")
                .location("Lyon, France")
                .companyName("Innovation Ltd")
                .industries(Arrays.asList("Healthcare"))
                .yearsAsLeader(8)
                .totalEmployees(200)
                .password("SecurePass456!")
                .build();
        
        ceoService.createCeoProfile(secondRequest, "test-keycloak-id-2");

        // When
        Page<CeoProfileResponse> result = ceoService.getAllCeos(PageRequest.of(0, 10));

        // Then
        assertThat(result.getContent()).hasSize(2);
        assertThat(result.getTotalElements()).isEqualTo(2);
    }

    @Test
    void testSearchCeos() {
        // Given
        ceoService.createCeoProfile(createRequest, testKeycloakId);

        // When
        Page<CeoProfileResponse> result = ceoService.searchCeos("Tech", PageRequest.of(0, 10));

        // Then
        assertThat(result.getContent()).hasSize(1);
        assertThat(result.getContent().get(0).getCompanyName()).contains("Tech");
    }

    @Test
    void testUpdateCeoProfile() {
        // Given
        CeoProfileResponse created = ceoService.createCeoProfile(createRequest, testKeycloakId);
        
        CeoUpdateRequest updateRequest = CeoUpdateRequest.builder()
                .companyName("Tech Corp International")
                .totalEmployees(750)
                .location("Paris Updated, France")
                .build();

        // When
        CeoProfileResponse updated = ceoService.updateCeo(created.getId(), updateRequest);

        // Then
        assertThat(updated.getCompanyName()).isEqualTo("Tech Corp International");
        assertThat(updated.getTotalEmployees()).isEqualTo(750);
        assertThat(updated.getLocation()).isEqualTo("Paris Updated, France");
    }

    @Test
    void testDeleteCeoProfile() {
        // Given
        CeoProfileResponse created = ceoService.createCeoProfile(createRequest, testKeycloakId);

        // When
        ceoService.deleteCeo(created.getId());

        // Then
        assertThat(ceoService.countCeos()).isEqualTo(0);
        assertFalse(ceoService.existsByKeycloakId(testKeycloakId));
    }

    @Test
    void testGetCeosByLocation() {
        // Given
        ceoService.createCeoProfile(createRequest, testKeycloakId);

        // When
        Page<CeoProfileResponse> result = ceoService.getCeosByLocation("Paris", PageRequest.of(0, 10));

        // Then
        assertThat(result.getContent()).hasSize(1);
        assertThat(result.getContent().get(0).getLocation()).contains("Paris");
    }

    @Test
    void testGetCeosByExperience() {
        // Given
        ceoService.createCeoProfile(createRequest, testKeycloakId);

        // When
        Page<CeoProfileResponse> result = ceoService.getCeosByExperience(5, PageRequest.of(0, 10));

        // Then
        assertThat(result.getContent()).hasSize(1);
        assertThat(result.getContent().get(0).getYearsAsLeader()).isGreaterThanOrEqualTo(5);
    }

    @Test
    void testCountCeos() {
        // Given
        ceoService.createCeoProfile(createRequest, testKeycloakId);

        // When
        long count = ceoService.countCeos();

        // Then
        assertThat(count).isEqualTo(1);
    }
}
