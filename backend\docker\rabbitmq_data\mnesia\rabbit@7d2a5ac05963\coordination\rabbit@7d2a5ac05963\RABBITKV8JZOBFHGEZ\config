#{id => {rabbitmq_metadata,rabbit@7d2a5ac05963},
  machine =>
      {module,khepri_machine,
              #{member => {rabbitmq_metadata,rabbit@7d2a5ac05963},
                store_id => rabbitmq_metadata}},
  friendly_name => "RabbitMQ metadata store",
  cluster_name => rabbitmq_metadata,uid => <<"RABBITKV8JZOBFHGEZ">>,
  initial_members => [],
  log_init_args => #{uid => <<"RABBITKV8JZOBFHGEZ">>},
  tick_timeout => 1000,broadcast_time => 100,
  install_snap_rpc_timeout => 120000,await_condition_timeout => 30000}.