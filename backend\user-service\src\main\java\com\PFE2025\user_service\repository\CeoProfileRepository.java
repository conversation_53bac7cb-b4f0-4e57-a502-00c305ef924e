package com.PFE2025.user_service.repository;

import com.PFE2025.user_service.model.CeoProfile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository pour la gestion des profils CEO.
 * Fournit les opérations CRUD et les requêtes spécialisées.
 */
@Repository
public interface CeoProfileRepository extends MongoRepository<CeoProfile, String> {

    /**
     * Trouve un profil CEO par son keycloakId
     */
    Optional<CeoProfile> findByKeycloakId(String keycloakId);

    /**
     * Vérifie si un profil existe pour un keycloakId donné
     */
    boolean existsByKeycloakId(String keycloakId);

    /**
     * Trouve tous les profils CEO avec pagination
     */
    Page<CeoProfile> findAll(Pageable pageable);

    /**
     * Recherche par nom ou prénom
     */
    @Query("{'$or': [{'firstName': {'$regex': ?0, '$options': 'i'}}, {'lastName': {'$regex': ?0, '$options': 'i'}}, {'fullName': {'$regex': ?0, '$options': 'i'}}]}")
    Page<CeoProfile> findByNameContaining(String name, Pageable pageable);

    /**
     * Recherche par nom d'entreprise
     */
    Page<CeoProfile> findByCompanyNameContainingIgnoreCase(String companyName, Pageable pageable);

    /**
     * Trouve les CEOs par localisation
     */
    Page<CeoProfile> findByLocationContainingIgnoreCase(String location, Pageable pageable);

    /**
     * Recherche combinée (nom, entreprise, localisation)
     */
    @Query("{'$or': [" +
           "{'firstName': {'$regex': ?0, '$options': 'i'}}, " +
           "{'lastName': {'$regex': ?0, '$options': 'i'}}, " +
           "{'fullName': {'$regex': ?0, '$options': 'i'}}, " +
           "{'companyName': {'$regex': ?0, '$options': 'i'}}, " +
           "{'location': {'$regex': ?0, '$options': 'i'}}" +
           "]}")
    Page<CeoProfile> searchByKeyword(String keyword, Pageable pageable);

    /**
     * Trouve les CEOs avec une expérience minimale en leadership
     */
    Page<CeoProfile> findByYearsAsLeaderGreaterThanEqual(Integer minYears, Pageable pageable);

    /**
     * Trouve les CEOs gérant plus d'un certain nombre d'employés
     */
    Page<CeoProfile> findByTotalEmployeesGreaterThanEqual(Integer minEmployees, Pageable pageable);

    /**
     * Trouve les CEOs par secteur d'activité
     */
    Page<CeoProfile> findByIndustriesContaining(String industry, Pageable pageable);

    /**
     * Compte le nombre total de CEOs
     */
    long count();

    /**
     * Trouve toutes les entreprises distinctes
     */
    @Query(value = "{}", fields = "{'companyName': 1}")
    List<CeoProfile> findDistinctCompanies();

    /**
     * Trouve tous les secteurs d'activité distincts
     */
    @Query(value = "{}", fields = "{'industries': 1}")
    List<CeoProfile> findDistinctIndustries();
}
