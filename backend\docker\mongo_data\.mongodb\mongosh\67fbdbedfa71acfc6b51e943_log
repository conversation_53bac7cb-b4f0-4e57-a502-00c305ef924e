{"t":{"$date":"2025-04-13T15:44:45.532Z"},"s":"I","c":"<PERSON><PERSON>G<PERSON><PERSON>","id":1000000048,"ctx":"config","msg":"Loading global configuration file","attr":{"filename":"/etc/mongosh.conf","found":false}}
{"t":{"$date":"2025-04-13T15:44:45.547Z"},"s":"I","c":"MONGOSH","id":1000000000,"ctx":"log","msg":"Starting log","attr":{"execPath":"/usr/bin/mongosh","envInfo":{"EDITOR":null,"NODE_OPTIONS":null,"TERM":null},"version":"2.4.0","distributionKind":"compiled","buildArch":"x64","buildPlatform":"linux","buildTarget":"linux-x64","buildTime":"2025-02-20T14:52:12.833Z","gitVersion":"df27287829c8ead52766ea931f85dd2a9928e8a2","nodeVersion":"v20.18.3","opensslVersion":"3.0.15+quic","sharedOpenssl":false,"runtimeArch":"x64","runtimePlatform":"linux","runtimeGlibcVersion":"2.39","deps":{"nodeDriverVersion":"6.13.0","libmongocryptVersion":"1.11.0","libmongocryptNodeBindingsVersion":"6.1.1","kerberosVersion":"2.1.0"}}}
{"t":{"$date":"2025-04-13T15:44:45.802Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000049,"ctx":"mongosh-connect","msg":"Loaded system CA list","attr":{"caCount":298,"asyncFallbackError":null,"systemCertsError":null,"messages":[]}}
{"t":{"$date":"2025-04-13T15:44:45.841Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000042,"ctx":"mongosh-connect","msg":"Initiating connection attempt","attr":{"uri":"mongodb://127.0.0.1:27017/admin?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh+2.4.0","driver":{"name":"nodejs|mongosh","version":"6.13.0|2.4.0"},"devtoolsConnectVersion":"3.4.1","host":"127.0.0.1:27017"}}
{"t":{"$date":"2025-04-13T15:44:45.858Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000035,"ctx":"mongosh-connect","msg":"Server heartbeat succeeded","attr":{"connectionId":"127.0.0.1:27017"}}
{"t":{"$date":"2025-04-13T15:44:45.997Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000037,"ctx":"mongosh-connect","msg":"Connection attempt finished"}
{"t":{"$date":"2025-04-13T15:44:46Z"},"s":"I","c":"MONGOSH","id":1000000010,"ctx":"shell-api","msg":"Initialized context","attr":{"method":"setCtx","arguments":{}}}
{"t":{"$date":"2025-04-13T15:44:46.010Z"},"s":"I","c":"MONGOSH-SNIPPETS","id":1000000019,"ctx":"snippets","msg":"Loaded snippets","attr":{"installdir":"/data/db/.mongodb/mongosh/snippets"}}
