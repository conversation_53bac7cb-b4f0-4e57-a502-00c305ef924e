package com.PFE2025.user_service.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.*;

/**
 * DTO pour la création d'un profil CEO complet.
 * Les données de base (username) sont générées automatiquement depuis l'email.
 * NOTE: Pas de département car le CEO supervise toute l'organisation.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Données pour créer un profil CEO complet")
public class CeoCreateRequest {

    // === DONNÉES DE BASE (REQUISES) ===
    // Le username est généré automatiquement depuis l'email

    @NotBlank(message = "Le prénom est requis")
    @Size(max = 50, message = "Le prénom ne peut pas dépasser 50 caractères")
    @Schema(description = "Prénom", example = "Jean", required = true)
    private String firstName;

    @NotBlank(message = "Le nom de famille est requis")
    @Size(max = 50, message = "Le nom de famille ne peut pas dépasser 50 caractères")
    @Schema(description = "Nom de famille", example = "Dupont", required = true)
    private String lastName;

    @NotBlank(message = "L'email est requis")
    @Email(message = "L'email doit être valide")
    @Schema(description = "Adresse email", example = "<EMAIL>", required = true)
    private String email;

    @NotBlank(message = "Le mot de passe est requis")
    @Size(min = 8, message = "Le mot de passe doit contenir au moins 8 caractères")
    @Pattern(regexp = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=!*]).*$", 
             message = "Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial")
    @Schema(description = "Mot de passe", example = "SecurePass123!", required = true)
    private String password;

    @Pattern(regexp = "^\\+?[0-9\\s()-]{8,20}$", message = "Le numéro de téléphone n'est pas valide")
    @Schema(description = "Numéro de téléphone", example = "+33123456789")
    private String phone;

    @Size(max = 100, message = "La localisation ne peut pas dépasser 100 caractères")
    @Schema(description = "Localisation/Ville", example = "Paris, France")
    private String location;

    // === DONNÉES COMPTE ===
    
    @Builder.Default
    @Schema(description = "État initial du compte", example = "true")
    private Boolean enabled = true;

    @Builder.Default
    @Schema(description = "Email vérifié", example = "true")
    private Boolean emailVerified = true;

    // === CHAMPS SPÉCIFIQUES CEO ===
    // NOTE: Pas de département - le CEO supervise toute l'organisation
    // Profil simplifié - seuls les champs de base sont conservés
}
