package com.PFE2025.user_service.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.*;
import java.util.List;

/**
 * DTO pour la création d'un profil CEO complet.
 * Les données de base (username) sont générées automatiquement depuis l'email.
 * NOTE: Pas de département car le CEO supervise toute l'organisation.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Données pour créer un profil CEO complet")
public class CeoCreateRequest {

    // === DONNÉES DE BASE (REQUISES) ===
    // Le username est généré automatiquement depuis l'email

    @NotBlank(message = "Le prénom est requis")
    @Size(max = 50, message = "Le prénom ne peut pas dépasser 50 caractères")
    @Schema(description = "Prénom", example = "Jean", required = true)
    private String firstName;

    @NotBlank(message = "Le nom de famille est requis")
    @Size(max = 50, message = "Le nom de famille ne peut pas dépasser 50 caractères")
    @Schema(description = "Nom de famille", example = "Dupont", required = true)
    private String lastName;

    @NotBlank(message = "L'email est requis")
    @Email(message = "L'email doit être valide")
    @Schema(description = "Adresse email", example = "<EMAIL>", required = true)
    private String email;

    @NotBlank(message = "Le mot de passe est requis")
    @Size(min = 8, message = "Le mot de passe doit contenir au moins 8 caractères")
    @Pattern(regexp = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=!*]).*$", 
             message = "Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial")
    @Schema(description = "Mot de passe", example = "SecurePass123!", required = true)
    private String password;

    @Pattern(regexp = "^\\+?[0-9\\s()-]{8,20}$", message = "Le numéro de téléphone n'est pas valide")
    @Schema(description = "Numéro de téléphone", example = "+33123456789")
    private String phone;

    @Size(max = 100, message = "La localisation ne peut pas dépasser 100 caractères")
    @Schema(description = "Localisation/Ville", example = "Paris, France")
    private String location;

    // === DONNÉES COMPTE ===
    
    @Builder.Default
    @Schema(description = "État initial du compte", example = "true")
    private Boolean enabled = true;

    @Builder.Default
    @Schema(description = "Email vérifié", example = "true")
    private Boolean emailVerified = true;

    // === CHAMPS SPÉCIFIQUES CEO ===
    // NOTE: Pas de département - le CEO supervise toute l'organisation
    
    @NotBlank(message = "Le nom de l'entreprise est requis")
    @Size(max = 100, message = "Le nom de l'entreprise ne peut pas dépasser 100 caractères")
    @Schema(description = "Nom de l'entreprise", example = "Vermeg Technologies", required = true)
    private String companyName;

    @Min(value = 1, message = "Le nombre d'employés doit être au moins 1")
    @Max(value = 100000, message = "Le nombre d'employés ne peut pas dépasser 100000")
    @Schema(description = "Nombre total d'employés sous sa responsabilité", example = "500")
    private Integer totalEmployees;

    @Schema(description = "Objectifs stratégiques", example = "[\"Expansion internationale\", \"Innovation technologique\"]")
    private List<String> strategicObjectives;

    @Min(value = 0, message = "Les années d'expérience ne peuvent pas être négatives")
    @Max(value = 50, message = "Les années d'expérience ne peuvent pas dépasser 50")
    @Schema(description = "Années d'expérience en leadership", example = "15")
    private Integer yearsAsLeader;

    @Schema(description = "Entreprises précédentes dirigées", example = "[\"TechCorp SA\", \"InnovateLtd\"]")
    private List<String> previousCompanies;

    @Schema(description = "Certifications de leadership/management", example = "[\"Executive MBA\", \"Leadership Certificate\"]")
    private List<String> certifications;

    @Schema(description = "Participations à des conseils d'administration", example = "[\"Board Member - TechAssociation\"]")
    private List<String> boardMemberships;

    @Size(max = 500, message = "La vision ne peut pas dépasser 500 caractères")
    @Schema(description = "Vision de l'entreprise", example = "Devenir leader mondial de l'innovation technologique")
    private String vision;

    @Schema(description = "Réalisations clés", example = "[\"Croissance de 200% en 3 ans\", \"Expansion en Europe\"]")
    private List<String> keyAchievements;

    @DecimalMin(value = "0.0", message = "Le chiffre d'affaires ne peut pas être négatif")
    @Schema(description = "Chiffre d'affaires annuel sous sa direction (en euros)", example = "50000000.0")
    private Double annualRevenue;

    @Schema(description = "Secteurs d'activité d'expérience", example = "[\"Technology\", \"Finance\", \"Consulting\"]")
    private List<String> industries;
}
