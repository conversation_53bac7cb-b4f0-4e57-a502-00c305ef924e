package com.PFE2025.user_service.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO de réponse pour les profils CEO.
 * Contient toutes les informations du CEO sans champ département.
 * Les données de base (firstName, lastName, email) viennent de Keycloak via /auth/me.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Profil complet d'un CEO")
public class CeoProfileResponse {

    // === IDENTIFIANTS ===
    
    @Schema(description = "ID unique du profil", example = "507f1f77bcf86cd799439011")
    private String id;

    @Schema(description = "ID Keycloak de l'utilisateur", example = "123e4567-e89b-12d3-a456-426614174000")
    private String keycloakId;

    // === DONNÉES DE BASE ===
    
    @Schema(description = "Prénom", example = "Jean")
    private String firstName;

    @Schema(description = "Nom de famille", example = "Dupont")
    private String lastName;

    @Schema(description = "Nom complet (prénom + nom)", example = "Jean Dupont")
    private String fullName;

    @Schema(description = "Adresse email", example = "<EMAIL>")
    private String email;

    @Schema(description = "Rôles utilisateur", example = "[\"CEO\"]")
    private List<String> roles;

    @Schema(description = "Type d'utilisateur", example = "INTERNAL")
    private String userType;

    @Schema(description = "Statut du compte", example = "ACTIVE")
    private String status;

    @Schema(description = "Localisation/Ville", example = "Paris, France")
    private String location;

    @Schema(description = "Numéro de téléphone", example = "+33123456789")
    private String phone;

    // === CHAMPS SPÉCIFIQUES CEO ===
    // NOTE: Pas de département - le CEO supervise toute l'organisation
    
    @Schema(description = "Nom de l'entreprise", example = "Vermeg Technologies")
    private String companyName;

    @Schema(description = "Nombre total d'employés sous sa responsabilité", example = "500")
    private Integer totalEmployees;

    @Schema(description = "Objectifs stratégiques", example = "[\"Expansion internationale\", \"Innovation technologique\"]")
    private List<String> strategicObjectives;

    @Schema(description = "Années d'expérience en leadership", example = "15")
    private Integer yearsAsLeader;

    @Schema(description = "Entreprises précédentes dirigées", example = "[\"TechCorp SA\", \"InnovateLtd\"]")
    private List<String> previousCompanies;

    @Schema(description = "Certifications de leadership/management", example = "[\"Executive MBA\", \"Leadership Certificate\"]")
    private List<String> certifications;

    @Schema(description = "Participations à des conseils d'administration", example = "[\"Board Member - TechAssociation\"]")
    private List<String> boardMemberships;

    @Schema(description = "Vision de l'entreprise", example = "Devenir leader mondial de l'innovation technologique")
    private String vision;

    @Schema(description = "Réalisations clés", example = "[\"Croissance de 200% en 3 ans\", \"Expansion en Europe\"]")
    private List<String> keyAchievements;

    @Schema(description = "Chiffre d'affaires annuel sous sa direction (en euros)", example = "50000000.0")
    private Double annualRevenue;

    @Schema(description = "Secteurs d'activité d'expérience", example = "[\"Technology\", \"Finance\", \"Consulting\"]")
    private List<String> industries;

    // === MÉTADONNÉES ===

    @Schema(description = "Date de création du profil")
    private LocalDateTime createdAt;

    @Schema(description = "Date de dernière mise à jour")
    private LocalDateTime updatedAt;

    // === CHAMPS CALCULÉS ===

    @Schema(description = "Score de leadership (0-100)", example = "95")
    private Integer leadershipScore;

    @Schema(description = "Niveau d'expérience", example = "EXECUTIVE")
    private String experienceLevel;

    @Schema(description = "Portée de responsabilité", example = "GLOBAL")
    private String responsibilityScope;

    /**
     * Calcule le score de leadership basé sur l'expérience et les réalisations
     */
    public void calculateLeadershipScore() {
        int score = 0;
        if (yearsAsLeader != null) {
            score += Math.min(yearsAsLeader * 5, 50); // Max 50 points pour l'expérience
        }
        if (previousCompanies != null) {
            score += Math.min(previousCompanies.size() * 10, 30); // Max 30 points pour la diversité
        }
        if (keyAchievements != null) {
            score += Math.min(keyAchievements.size() * 5, 20); // Max 20 points pour les réalisations
        }
        this.leadershipScore = Math.min(score, 100);
    }

    /**
     * Détermine le niveau d'expérience basé sur les années de leadership
     */
    public void calculateExperienceLevel() {
        if (yearsAsLeader == null) {
            this.experienceLevel = "UNKNOWN";
        } else if (yearsAsLeader < 5) {
            this.experienceLevel = "EMERGING";
        } else if (yearsAsLeader < 10) {
            this.experienceLevel = "EXPERIENCED";
        } else if (yearsAsLeader < 20) {
            this.experienceLevel = "SENIOR";
        } else {
            this.experienceLevel = "EXECUTIVE";
        }
    }

    /**
     * Détermine la portée de responsabilité basée sur le nombre d'employés
     */
    public void calculateResponsibilityScope() {
        if (totalEmployees == null) {
            this.responsibilityScope = "UNKNOWN";
        } else if (totalEmployees < 50) {
            this.responsibilityScope = "LOCAL";
        } else if (totalEmployees < 200) {
            this.responsibilityScope = "REGIONAL";
        } else if (totalEmployees < 1000) {
            this.responsibilityScope = "NATIONAL";
        } else {
            this.responsibilityScope = "GLOBAL";
        }
    }

    /**
     * Met à jour automatiquement le fullName à partir du firstName et lastName
     */
    public void updateFullName() {
        if (firstName != null && lastName != null) {
            this.fullName = firstName + " " + lastName;
        }
    }
}
