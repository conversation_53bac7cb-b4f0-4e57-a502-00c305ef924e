package com.PFE2025.user_service.util;

import com.PFE2025.user_service.dto.request.CandidateRegistrationRequest;
import com.PFE2025.user_service.dto.response.CandidateProfileResponse;
import com.PFE2025.user_service.model.CandidateProfile;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T14:30:44+0100",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.8 (Oracle Corporation)"
)
@Component
public class CandidateProfileMapperImpl implements CandidateProfileMapper {

    @Override
    public CandidateProfile fromRegistrationRequest(CandidateRegistrationRequest request) {
        if ( request == null ) {
            return null;
        }

        CandidateProfile.CandidateProfileBuilder<?, ?> candidateProfile = CandidateProfile.builder();

        candidateProfile.firstName( request.getFirstName() );
        candidateProfile.lastName( request.getLastName() );
        candidateProfile.email( request.getEmail() );
        candidateProfile.location( request.getLocation() );
        candidateProfile.phone( request.getPhone() );
        candidateProfile.linkedinUrl( request.getLinkedinUrl() );
        candidateProfile.portfolioUrl( request.getPortfolioUrl() );
        candidateProfile.dateOfBirth( request.getDateOfBirth() );
        List<String> list = request.getPreferredCategories();
        if ( list != null ) {
            candidateProfile.preferredCategories( new ArrayList<String>( list ) );
        }

        candidateProfile.photo( "" );

        return candidateProfile.build();
    }

    @Override
    public CandidateProfileResponse toResponse(CandidateProfile profile) {
        if ( profile == null ) {
            return null;
        }

        CandidateProfileResponse.CandidateProfileResponseBuilder candidateProfileResponse = CandidateProfileResponse.builder();

        candidateProfileResponse.experiences( toExperienceDtoList( profile.getExperiences() ) );
        candidateProfileResponse.educationHistory( toEducationDtoList( profile.getEducationHistory() ) );
        candidateProfileResponse.certifications( toCertificationDtoList( profile.getCertifications() ) );
        candidateProfileResponse.languages( toLanguageProficiencyDtoList( profile.getLanguages() ) );
        candidateProfileResponse.id( profile.getId() );
        candidateProfileResponse.keycloakId( profile.getKeycloakId() );
        candidateProfileResponse.firstName( profile.getFirstName() );
        candidateProfileResponse.lastName( profile.getLastName() );
        candidateProfileResponse.email( profile.getEmail() );
        candidateProfileResponse.location( profile.getLocation() );
        candidateProfileResponse.phone( profile.getPhone() );
        candidateProfileResponse.linkedinUrl( profile.getLinkedinUrl() );
        candidateProfileResponse.portfolioUrl( profile.getPortfolioUrl() );
        candidateProfileResponse.dateOfBirth( profile.getDateOfBirth() );
        List<String> list4 = profile.getPreferredCategories();
        if ( list4 != null ) {
            candidateProfileResponse.preferredCategories( new ArrayList<String>( list4 ) );
        }
        candidateProfileResponse.photo( profile.getPhoto() );
        candidateProfileResponse.documentCVId( profile.getDocumentCVId() );
        List<String> list5 = profile.getSkills();
        if ( list5 != null ) {
            candidateProfileResponse.skills( new ArrayList<String>( list5 ) );
        }
        candidateProfileResponse.seniorityLevel( profile.getSeniorityLevel() );
        candidateProfileResponse.yearsOfExperience( profile.getYearsOfExperience() );
        candidateProfileResponse.profileSummary( profile.getProfileSummary() );
        candidateProfileResponse.cvLanguage( profile.getCvLanguage() );
        candidateProfileResponse.overallScore( profile.getOverallScore() );
        candidateProfileResponse.overallAssessment( profile.getOverallAssessment() );
        List<String> list6 = profile.getStrengths();
        if ( list6 != null ) {
            candidateProfileResponse.strengths( new ArrayList<String>( list6 ) );
        }
        List<String> list7 = profile.getWeaknesses();
        if ( list7 != null ) {
            candidateProfileResponse.weaknesses( new ArrayList<String>( list7 ) );
        }
        List<String> list8 = profile.getRecommendations();
        if ( list8 != null ) {
            candidateProfileResponse.recommendations( new ArrayList<String>( list8 ) );
        }
        candidateProfileResponse.atsCompatibility( profile.getAtsCompatibility() );
        List<String> list9 = profile.getMissingKeywords();
        if ( list9 != null ) {
            candidateProfileResponse.missingKeywords( new ArrayList<String>( list9 ) );
        }
        candidateProfileResponse.improvementPriority( profile.getImprovementPriority() );
        candidateProfileResponse.formatScore( profile.getFormatScore() );
        candidateProfileResponse.contentScore( profile.getContentScore() );
        candidateProfileResponse.skillsScore( profile.getSkillsScore() );
        candidateProfileResponse.experienceScore( profile.getExperienceScore() );
        candidateProfileResponse.scoreExplanation( profile.getScoreExplanation() );
        candidateProfileResponse.createdAt( profile.getCreatedAt() );
        candidateProfileResponse.updatedAt( profile.getUpdatedAt() );
        candidateProfileResponse.cvProcessedAt( profile.getCvProcessedAt() );
        candidateProfileResponse.aiModelUsed( profile.getAiModelUsed() );
        candidateProfileResponse.cvDetectedLanguage( profile.getCvDetectedLanguage() );

        candidateProfileResponse.fullName( profile.getFullName() );
        candidateProfileResponse.userType( "CANDIDATE" );

        return candidateProfileResponse.build();
    }

    @Override
    public CandidateProfileResponse.ExperienceDto toExperienceDto(CandidateProfile.Experience experience) {
        if ( experience == null ) {
            return null;
        }

        CandidateProfileResponse.ExperienceDto.ExperienceDtoBuilder experienceDto = CandidateProfileResponse.ExperienceDto.builder();

        experienceDto.company( experience.getCompany() );
        experienceDto.position( experience.getPosition() );
        experienceDto.startDate( experience.getStartDate() );
        experienceDto.endDate( experience.getEndDate() );
        experienceDto.description( experience.getDescription() );

        return experienceDto.build();
    }

    @Override
    public CandidateProfileResponse.EducationDto toEducationDto(CandidateProfile.Education education) {
        if ( education == null ) {
            return null;
        }

        CandidateProfileResponse.EducationDto.EducationDtoBuilder educationDto = CandidateProfileResponse.EducationDto.builder();

        educationDto.degree( education.getDegree() );
        educationDto.institution( education.getInstitution() );
        educationDto.field( education.getField() );
        educationDto.year( education.getYear() );

        return educationDto.build();
    }

    @Override
    public CandidateProfileResponse.CertificationDto toCertificationDto(CandidateProfile.Certification certification) {
        if ( certification == null ) {
            return null;
        }

        CandidateProfileResponse.CertificationDto.CertificationDtoBuilder certificationDto = CandidateProfileResponse.CertificationDto.builder();

        certificationDto.name( certification.getName() );
        certificationDto.issuer( certification.getIssuer() );
        certificationDto.date( certification.getDate() );

        return certificationDto.build();
    }

    @Override
    public CandidateProfileResponse.LanguageProficiencyDto toLanguageProficiencyDto(CandidateProfile.LanguageProficiency language) {
        if ( language == null ) {
            return null;
        }

        CandidateProfileResponse.LanguageProficiencyDto.LanguageProficiencyDtoBuilder languageProficiencyDto = CandidateProfileResponse.LanguageProficiencyDto.builder();

        languageProficiencyDto.language( language.getLanguage() );
        languageProficiencyDto.proficiency( language.getProficiency() );

        return languageProficiencyDto.build();
    }

    @Override
    public void updateFromRegistrationRequest(CandidateProfile profile, CandidateRegistrationRequest request) {
        if ( request == null ) {
            return;
        }

        if ( request.getFirstName() != null ) {
            profile.setFirstName( request.getFirstName() );
        }
        if ( request.getLastName() != null ) {
            profile.setLastName( request.getLastName() );
        }
        if ( request.getEmail() != null ) {
            profile.setEmail( request.getEmail() );
        }
        if ( request.getLocation() != null ) {
            profile.setLocation( request.getLocation() );
        }
        if ( request.getPhone() != null ) {
            profile.setPhone( request.getPhone() );
        }
        if ( request.getLinkedinUrl() != null ) {
            profile.setLinkedinUrl( request.getLinkedinUrl() );
        }
        if ( request.getPortfolioUrl() != null ) {
            profile.setPortfolioUrl( request.getPortfolioUrl() );
        }
        if ( request.getDateOfBirth() != null ) {
            profile.setDateOfBirth( request.getDateOfBirth() );
        }
        if ( profile.getPreferredCategories() != null ) {
            List<String> list = request.getPreferredCategories();
            if ( list != null ) {
                profile.getPreferredCategories().clear();
                profile.getPreferredCategories().addAll( list );
            }
        }
        else {
            List<String> list = request.getPreferredCategories();
            if ( list != null ) {
                profile.setPreferredCategories( new ArrayList<String>( list ) );
            }
        }
    }
}
