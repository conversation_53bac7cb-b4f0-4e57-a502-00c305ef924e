package com.PFE2025.user_service.util;

import com.PFE2025.user_service.dto.request.CandidateRegistrationRequest;
import com.PFE2025.user_service.dto.response.CandidateProfileResponse;
import com.PFE2025.user_service.model.CandidateProfile;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T14:18:04+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class CandidateProfileMapperImpl implements CandidateProfileMapper {

    @Override
    public CandidateProfile fromRegistrationRequest(CandidateRegistrationRequest request) {
        if ( request == null ) {
            return null;
        }

        CandidateProfile.CandidateProfileBuilder<?, ?> candidateProfile = CandidateProfile.builder();

        candidateProfile.dateOfBirth( request.getDateOfBirth() );
        candidateProfile.email( request.getEmail() );
        candidateProfile.firstName( request.getFirstName() );
        candidateProfile.lastName( request.getLastName() );
        candidateProfile.linkedinUrl( request.getLinkedinUrl() );
        candidateProfile.location( request.getLocation() );
        candidateProfile.phone( request.getPhone() );
        candidateProfile.portfolioUrl( request.getPortfolioUrl() );
        List<String> list = request.getPreferredCategories();
        if ( list != null ) {
            candidateProfile.preferredCategories( new ArrayList<String>( list ) );
        }

        candidateProfile.photo( "" );

        return candidateProfile.build();
    }

    @Override
    public CandidateProfileResponse toResponse(CandidateProfile profile) {
        if ( profile == null ) {
            return null;
        }

        CandidateProfileResponse.CandidateProfileResponseBuilder candidateProfileResponse = CandidateProfileResponse.builder();

        candidateProfileResponse.experiences( toExperienceDtoList( profile.getExperiences() ) );
        candidateProfileResponse.educationHistory( toEducationDtoList( profile.getEducationHistory() ) );
        candidateProfileResponse.certifications( toCertificationDtoList( profile.getCertifications() ) );
        candidateProfileResponse.languages( toLanguageProficiencyDtoList( profile.getLanguages() ) );
        candidateProfileResponse.aiModelUsed( profile.getAiModelUsed() );
        candidateProfileResponse.atsCompatibility( profile.getAtsCompatibility() );
        candidateProfileResponse.contentScore( profile.getContentScore() );
        candidateProfileResponse.createdAt( profile.getCreatedAt() );
        candidateProfileResponse.cvDetectedLanguage( profile.getCvDetectedLanguage() );
        candidateProfileResponse.cvLanguage( profile.getCvLanguage() );
        candidateProfileResponse.cvProcessedAt( profile.getCvProcessedAt() );
        candidateProfileResponse.dateOfBirth( profile.getDateOfBirth() );
        candidateProfileResponse.documentCVId( profile.getDocumentCVId() );
        candidateProfileResponse.email( profile.getEmail() );
        candidateProfileResponse.experienceScore( profile.getExperienceScore() );
        candidateProfileResponse.firstName( profile.getFirstName() );
        candidateProfileResponse.formatScore( profile.getFormatScore() );
        candidateProfileResponse.id( profile.getId() );
        candidateProfileResponse.improvementPriority( profile.getImprovementPriority() );
        candidateProfileResponse.keycloakId( profile.getKeycloakId() );
        candidateProfileResponse.lastName( profile.getLastName() );
        candidateProfileResponse.linkedinUrl( profile.getLinkedinUrl() );
        candidateProfileResponse.location( profile.getLocation() );
        List<String> list4 = profile.getMissingKeywords();
        if ( list4 != null ) {
            candidateProfileResponse.missingKeywords( new ArrayList<String>( list4 ) );
        }
        candidateProfileResponse.overallAssessment( profile.getOverallAssessment() );
        candidateProfileResponse.overallScore( profile.getOverallScore() );
        candidateProfileResponse.phone( profile.getPhone() );
        candidateProfileResponse.photo( profile.getPhoto() );
        candidateProfileResponse.portfolioUrl( profile.getPortfolioUrl() );
        List<String> list5 = profile.getPreferredCategories();
        if ( list5 != null ) {
            candidateProfileResponse.preferredCategories( new ArrayList<String>( list5 ) );
        }
        candidateProfileResponse.profileSummary( profile.getProfileSummary() );
        List<String> list6 = profile.getRecommendations();
        if ( list6 != null ) {
            candidateProfileResponse.recommendations( new ArrayList<String>( list6 ) );
        }
        candidateProfileResponse.scoreExplanation( profile.getScoreExplanation() );
        candidateProfileResponse.seniorityLevel( profile.getSeniorityLevel() );
        List<String> list7 = profile.getSkills();
        if ( list7 != null ) {
            candidateProfileResponse.skills( new ArrayList<String>( list7 ) );
        }
        candidateProfileResponse.skillsScore( profile.getSkillsScore() );
        List<String> list8 = profile.getStrengths();
        if ( list8 != null ) {
            candidateProfileResponse.strengths( new ArrayList<String>( list8 ) );
        }
        candidateProfileResponse.updatedAt( profile.getUpdatedAt() );
        List<String> list9 = profile.getWeaknesses();
        if ( list9 != null ) {
            candidateProfileResponse.weaknesses( new ArrayList<String>( list9 ) );
        }
        candidateProfileResponse.yearsOfExperience( profile.getYearsOfExperience() );

        candidateProfileResponse.fullName( profile.getFullName() );
        candidateProfileResponse.userType( "CANDIDATE" );

        return candidateProfileResponse.build();
    }

    @Override
    public CandidateProfileResponse.ExperienceDto toExperienceDto(CandidateProfile.Experience experience) {
        if ( experience == null ) {
            return null;
        }

        CandidateProfileResponse.ExperienceDto.ExperienceDtoBuilder experienceDto = CandidateProfileResponse.ExperienceDto.builder();

        experienceDto.company( experience.getCompany() );
        experienceDto.description( experience.getDescription() );
        experienceDto.endDate( experience.getEndDate() );
        experienceDto.position( experience.getPosition() );
        experienceDto.startDate( experience.getStartDate() );

        return experienceDto.build();
    }

    @Override
    public CandidateProfileResponse.EducationDto toEducationDto(CandidateProfile.Education education) {
        if ( education == null ) {
            return null;
        }

        CandidateProfileResponse.EducationDto.EducationDtoBuilder educationDto = CandidateProfileResponse.EducationDto.builder();

        educationDto.degree( education.getDegree() );
        educationDto.field( education.getField() );
        educationDto.institution( education.getInstitution() );
        educationDto.year( education.getYear() );

        return educationDto.build();
    }

    @Override
    public CandidateProfileResponse.CertificationDto toCertificationDto(CandidateProfile.Certification certification) {
        if ( certification == null ) {
            return null;
        }

        CandidateProfileResponse.CertificationDto.CertificationDtoBuilder certificationDto = CandidateProfileResponse.CertificationDto.builder();

        certificationDto.date( certification.getDate() );
        certificationDto.issuer( certification.getIssuer() );
        certificationDto.name( certification.getName() );

        return certificationDto.build();
    }

    @Override
    public CandidateProfileResponse.LanguageProficiencyDto toLanguageProficiencyDto(CandidateProfile.LanguageProficiency language) {
        if ( language == null ) {
            return null;
        }

        CandidateProfileResponse.LanguageProficiencyDto.LanguageProficiencyDtoBuilder languageProficiencyDto = CandidateProfileResponse.LanguageProficiencyDto.builder();

        languageProficiencyDto.language( language.getLanguage() );
        languageProficiencyDto.proficiency( language.getProficiency() );

        return languageProficiencyDto.build();
    }

    @Override
    public void updateFromRegistrationRequest(CandidateProfile profile, CandidateRegistrationRequest request) {
        if ( request == null ) {
            return;
        }

        if ( request.getDateOfBirth() != null ) {
            profile.setDateOfBirth( request.getDateOfBirth() );
        }
        if ( request.getEmail() != null ) {
            profile.setEmail( request.getEmail() );
        }
        if ( request.getFirstName() != null ) {
            profile.setFirstName( request.getFirstName() );
        }
        if ( request.getLastName() != null ) {
            profile.setLastName( request.getLastName() );
        }
        if ( request.getLinkedinUrl() != null ) {
            profile.setLinkedinUrl( request.getLinkedinUrl() );
        }
        if ( request.getLocation() != null ) {
            profile.setLocation( request.getLocation() );
        }
        if ( request.getPhone() != null ) {
            profile.setPhone( request.getPhone() );
        }
        if ( request.getPortfolioUrl() != null ) {
            profile.setPortfolioUrl( request.getPortfolioUrl() );
        }
        if ( profile.getPreferredCategories() != null ) {
            List<String> list = request.getPreferredCategories();
            if ( list != null ) {
                profile.getPreferredCategories().clear();
                profile.getPreferredCategories().addAll( list );
            }
        }
        else {
            List<String> list = request.getPreferredCategories();
            if ( list != null ) {
                profile.setPreferredCategories( new ArrayList<String>( list ) );
            }
        }
    }
}
