package com.pfe2025.interview_service.mapper;

import com.pfe2025.interview_service.dto.FeedbackDTO;
import com.pfe2025.interview_service.model.InterviewFeedback;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T14:17:52+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class FeedbackMapperImpl implements FeedbackMapper {

    @Override
    public FeedbackDTO.FeedbackResponse toResponse(InterviewFeedback feedback) {
        if ( feedback == null ) {
            return null;
        }

        FeedbackDTO.FeedbackResponse.FeedbackResponseBuilder feedbackResponse = FeedbackDTO.FeedbackResponse.builder();

        feedbackResponse.feedbackRecommendation( feedback.getRecommendation() );
        feedbackResponse.communicationComments( feedback.getCommunicationComments() );
        feedbackResponse.communicationScore( feedback.getCommunicationScore() );
        feedbackResponse.createdAt( feedback.getCreatedAt() );
        feedbackResponse.culturalComments( feedback.getCulturalComments() );
        feedbackResponse.culturalScore( feedback.getCulturalScore() );
        feedbackResponse.evaluatorId( feedback.getEvaluatorId() );
        feedbackResponse.evaluatorName( feedback.getEvaluatorName() );
        feedbackResponse.generalComments( feedback.getGeneralComments() );
        feedbackResponse.hasSubmitted( feedback.getHasSubmitted() );
        feedbackResponse.id( feedback.getId() );
        feedbackResponse.recommendationReason( feedback.getRecommendationReason() );
        feedbackResponse.technicalComments( feedback.getTechnicalComments() );
        feedbackResponse.technicalScore( feedback.getTechnicalScore() );

        return feedbackResponse.build();
    }

    @Override
    public List<FeedbackDTO.FeedbackResponse> toResponseList(List<InterviewFeedback> feedbacks) {
        if ( feedbacks == null ) {
            return null;
        }

        List<FeedbackDTO.FeedbackResponse> list = new ArrayList<FeedbackDTO.FeedbackResponse>( feedbacks.size() );
        for ( InterviewFeedback interviewFeedback : feedbacks ) {
            list.add( toResponse( interviewFeedback ) );
        }

        return list;
    }

    @Override
    public InterviewFeedback fromRequest(FeedbackDTO.SubmitFeedbackRequest request) {
        if ( request == null ) {
            return null;
        }

        InterviewFeedback.InterviewFeedbackBuilder<?, ?> interviewFeedback = InterviewFeedback.builder();

        interviewFeedback.communicationComments( request.getCommunicationComments() );
        interviewFeedback.communicationScore( request.getCommunicationScore() );
        interviewFeedback.culturalComments( request.getCulturalComments() );
        interviewFeedback.culturalScore( request.getCulturalScore() );
        interviewFeedback.generalComments( request.getGeneralComments() );
        interviewFeedback.recommendation( request.getRecommendation() );
        interviewFeedback.recommendationReason( request.getRecommendationReason() );
        interviewFeedback.technicalComments( request.getTechnicalComments() );
        interviewFeedback.technicalScore( request.getTechnicalScore() );

        interviewFeedback.hasSubmitted( true );
        interviewFeedback.createdAt( java.time.LocalDateTime.now() );
        interviewFeedback.evaluatorId( getCurrentUserId() );
        interviewFeedback.evaluatorName( getCurrentUserName() );

        return interviewFeedback.build();
    }
}
