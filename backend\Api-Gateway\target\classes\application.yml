server:
  port: 7000
  error:
    include-message: always
    include-binding-errors: always

spring:
  application:
    name: api-gateway

  # Configuration Redis
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 2000ms

  # Configuration du cache
  cache:
    type: redis
    redis:
      time-to-live: 300000 # 5 minutes en millisecondes
      cache-null-values: false

  # Configuration Cloud Gateway
  cloud:
    gateway:
      # Configuration CORS globale
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origins:
              - "http://localhost:3000"
            allowed-methods:
              - GET
              - POST
              - PUT
              - DELETE
              - PATCH
              - OPTIONS
            allowed-headers:
              - "*"
            exposed-headers:
              - "Set-Cookie"
            allow-credentials: true
            max-age: 3600

        # IMPORTANT : Ajoutez ce filtre global
        default-filters:
          - DedupeResponseHeader=Access-Control-Allow-Credentials Access-Control-Allow-Origin Access-Control-Allow-Methods

      # Discovery activé pour Eureka
      discovery:
        locator:
          enabled: false # On va utiliser des routes explicites
          lower-case-service-id: true

# Configuration JWT/Keycloak
keycloak:
  auth-server-url: ${KEYCLOAK_URL:http://localhost:9090}
  realm: ${KEYCLOAK_REALM:vermeg-Platform}
  jwk-set-uri: ${keycloak.auth-server-url}/realms/${keycloak.realm}/protocol/openid-connect/certs

# Configuration de l'authentification
auth:
  # Pour valider avec auth-service si nécessaire
  service:
    url: ${AUTH_SERVICE_URL:http://localhost:7001}
    validate-endpoint: /auth/validate
    refresh-endpoint: /auth/refresh

  # Pour signer les headers internes
  internal:
    jwt-secret: ${INTERNAL_JWT_SECRET:VmVyeVNlY3JldEtleUZvckludGVybmFsVXNlT25seTEyMzQ1Njc4OTA=}
    jwt-expiration: 300 # 5 minutes

  # Configuration des cookies
  cookie:
    access-token-name: access_token
    refresh-token-name: refresh_token
    domain: ${COOKIE_DOMAIN:localhost}
    secure: ${COOKIE_SECURE:false}
    same-site: Lax

# Endpoints publics (pas d'authentification requise)
public-endpoints:
  paths: /api/auth/login,/api/auth/logout,/api/auth/refresh,/api/auth/validate,/api/auth/forgot-password/**,/api/auth/health,/api/users/register,/api/users/verify-email,/api/documents/upload,/api/documents/*/download,/swagger-ui.html,/swagger-ui/**,/v3/api-docs/**,/*/v3/api-docs,/actuator/health,/actuator/info,/api/public/**

# Configuration des rôles admin
admin:
  paths: /api/users/admin/**,/api/auth/users/**
  roles: ceo,RH_ADMIN

# Configuration Rate Limiting
rate-limiting:
  enabled: true
  default:
    limit: 100
    duration: 60 # secondes
  endpoints:
    - path: /api/auth/login
      limit: 5
      duration: 60
    - path: /api/auth/forgot-password/**
      limit: 3
      duration: 300
    - path: /api/users/register
      limit: 3
      duration: 300
    - path: /api/documents/upload
      limit: 10
      duration: 60
    - path: /api/documents/*/download
      limit: 20
      duration: 60

# Configuration Circuit Breaker
resilience4j:
  circuitbreaker:
    configs:
      default:
        sliding-window-size: 10
        failure-rate-threshold: 50
        wait-duration-in-open-state: 10s
        permitted-number-of-calls-in-half-open-state: 3
        automatic-transition-from-open-to-half-open-enabled: true
    instances:
      auth-service:
        base-config: default
        wait-duration-in-open-state: 5s
      user-service:
        base-config: default
      candidate-service:
        base-config: default
      document-management-service:
        base-config: default
        wait-duration-in-open-state: 8s

  timelimiter:
    configs:
      default:
        timeout-duration: 3s
    instances:
      auth-service:
        timeout-duration: 5s
      document-management-service:
        timeout-duration: 10s  # Plus long pour les uploads/downloads

# Configuration Eureka
eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI:http://localhost:8761/eureka}
    fetch-registry: true
    register-with-eureka: true
    # Optimisations pour développement (réduire en production)
    registry-fetch-interval-seconds: 10
    instance-info-replication-interval-seconds: 10
  instance:
    prefer-ip-address: true
    instance-id: ${spring.application.name}:${spring.application.instance_id:${random.value}}
    # Optimisations pour développement
    lease-renewal-interval-in-seconds: 10
    lease-expiration-duration-in-seconds: 30
    # Métadonnées utiles pour le monitoring
    metadata-map:
      version: "0.0.1-SNAPSHOT"
      description: "API Gateway for Vermeg recruitment platform"
      management.context-path: /actuator

# Configuration logging
logging:
  level:
    root: INFO
    com.PFE2025.api_gateway: DEBUG
    org.springframework.cloud.gateway: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

# Configuration Actuator
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,caches
  endpoint:
    health:
      show-details: when-authorized

# Configuration Springdoc
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    urls:
      - name: API Gateway
        url: /v3/api-docs
      - name: Auth Service
        url: /auth-service/v3/api-docs
      - name: User Service
        url: /user-service/v3/api-docs
      - name: Candidate Service
        url: /candidate-service/v3/api-docs
      - name: Document Service
        url: /document-service/v3/api-docs