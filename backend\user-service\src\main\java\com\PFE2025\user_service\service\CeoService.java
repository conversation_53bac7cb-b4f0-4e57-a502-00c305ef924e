package com.PFE2025.user_service.service;

import com.PFE2025.user_service.dto.request.CeoCreateRequest;
import com.PFE2025.user_service.dto.request.CeoUpdateRequest;
import com.PFE2025.user_service.dto.response.CeoProfileResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Interface de service pour la gestion des profils CEO.
 * Fournit les opérations CRUD et les fonctionnalités de recherche.
 */
public interface CeoService {

    /**
     * C<PERSON>e un nouveau profil CEO pour un keycloakId existant
     * @param request Donn<PERSON> du CEO à créer
     * @param keycloakId ID Keycloak de l'utilisateur existant
     * @return Profil CEO créé
     */
    CeoProfileResponse createCeoProfile(CeoCreateRequest request, String keycloakId);

    /**
     * Récup<PERSON> tous les profils CEO avec pagination
     * @param pageable Paramètres de pagination
     * @return Page de profils CEO
     */
    Page<CeoProfileResponse> getAllCeos(Pageable pageable);

    /**
     * Récupère un profil CEO par son ID
     * @param id ID du profil
     * @return Profil CEO
     */
    CeoProfileResponse getCeoById(String id);

    /**
     * Récupère un profil CEO par son keycloakId
     * @param keycloakId ID Keycloak
     * @return Profil CEO
     */
    CeoProfileResponse getCeoByKeycloakId(String keycloakId);

    /**
     * Met à jour un profil CEO
     * @param id ID du profil à mettre à jour
     * @param request Nouvelles données
     * @return Profil CEO mis à jour
     */
    CeoProfileResponse updateCeo(String id, CeoUpdateRequest request);

    /**
     * Supprime un profil CEO
     * @param id ID du profil à supprimer
     */
    void deleteCeo(String id);

    /**
     * Recherche des CEOs par mot-clé
     * @param keyword Mot-clé de recherche
     * @param pageable Paramètres de pagination
     * @return Page de profils CEO correspondants
     */
    Page<CeoProfileResponse> searchCeos(String keyword, Pageable pageable);

    /**
     * Récupère les CEOs par localisation
     * @param location Localisation
     * @param pageable Paramètres de pagination
     * @return Page de profils CEO
     */
    Page<CeoProfileResponse> getCeosByLocation(String location, Pageable pageable);

    /**
     * Récupère les CEOs par nom d'entreprise
     * @param companyName Nom de l'entreprise
     * @param pageable Paramètres de pagination
     * @return Page de profils CEO
     */
    Page<CeoProfileResponse> getCeosByCompany(String companyName, Pageable pageable);

    /**
     * Récupère les CEOs avec une expérience minimale
     * @param minYears Années d'expérience minimales
     * @param pageable Paramètres de pagination
     * @return Page de profils CEO
     */
    Page<CeoProfileResponse> getCeosByExperience(Integer minYears, Pageable pageable);

    /**
     * Récupère les CEOs gérant plus d'un certain nombre d'employés
     * @param minEmployees Nombre minimum d'employés
     * @param pageable Paramètres de pagination
     * @return Page de profils CEO
     */
    Page<CeoProfileResponse> getCeosByEmployeeCount(Integer minEmployees, Pageable pageable);

    /**
     * Récupère les CEOs par secteur d'activité
     * @param industry Secteur d'activité
     * @param pageable Paramètres de pagination
     * @return Page de profils CEO
     */
    Page<CeoProfileResponse> getCeosByIndustry(String industry, Pageable pageable);

    /**
     * Compte le nombre total de CEOs
     * @return Nombre de CEOs
     */
    long countCeos();

    /**
     * Vérifie si un profil CEO existe pour un keycloakId donné
     * @param keycloakId ID Keycloak
     * @return true si le profil existe
     */
    boolean existsByKeycloakId(String keycloakId);
}
