package com.pfe2025.application_service.mapper;

import com.pfe2025.application_service.dto.ApplicationDTO;
import com.pfe2025.application_service.dto.StatusHistoryDTO;
import com.pfe2025.application_service.model.Application;
import com.pfe2025.application_service.model.ApplicationStatusHistory;
import com.pfe2025.application_service.model.Evaluation;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T02:01:20+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApplicationMapperImpl implements ApplicationMapper {

    @Autowired
    private EvaluationMapper evaluationMapper;

    @Override
    public ApplicationDTO.DetailResponse toDetailResponse(Application application) {
        if ( application == null ) {
            return null;
        }

        ApplicationDTO.DetailResponse.DetailResponseBuilder<?, ?> detailResponse = ApplicationDTO.DetailResponse.builder();

        detailResponse.statusHistory( applicationStatusHistorySetToStatusHistoryDTOList( application.getStatusHistory() ) );
        detailResponse.createdAt( application.getCreatedAt() );
        detailResponse.createdBy( application.getCreatedBy() );
        detailResponse.id( application.getId() );
        detailResponse.updatedAt( application.getUpdatedAt() );
        detailResponse.updatedBy( application.getUpdatedBy() );
        detailResponse.aiProcessed( application.getAiProcessed() );
        detailResponse.aiScore( application.getAiScore() );
        detailResponse.autoDecision( application.getAutoDecision() );
        detailResponse.candidateId( application.getCandidateId() );
        detailResponse.candidateMessage( application.getCandidateMessage() );
        detailResponse.candidateName( application.getCandidateName() );
        detailResponse.evaluation( evaluationMapper.toEvaluationDTO( application.getEvaluation() ) );
        detailResponse.interviewId( application.getInterviewId() );
        detailResponse.interviewRequestedAt( application.getInterviewRequestedAt() );
        detailResponse.jobDepartment( application.getJobDepartment() );
        detailResponse.jobPostingId( application.getJobPostingId() );
        detailResponse.jobTitle( application.getJobTitle() );
        detailResponse.lastStatusChangedAt( application.getLastStatusChangedAt() );
        detailResponse.lastStatusChangedBy( application.getLastStatusChangedBy() );
        detailResponse.processedAt( application.getProcessedAt() );
        detailResponse.recruiterNotes( application.getRecruiterNotes() );
        detailResponse.reference( application.getReference() );
        detailResponse.status( application.getStatus() );
        detailResponse.submittedAt( application.getSubmittedAt() );

        return detailResponse.build();
    }

    @Override
    public ApplicationDTO.SummaryResponse toSummaryResponse(Application application) {
        if ( application == null ) {
            return null;
        }

        ApplicationDTO.SummaryResponse.SummaryResponseBuilder summaryResponse = ApplicationDTO.SummaryResponse.builder();

        summaryResponse.aiProcessed( application.getAiProcessed() );
        summaryResponse.aiScore( application.getAiScore() );
        summaryResponse.candidateName( application.getCandidateName() );
        summaryResponse.id( application.getId() );
        summaryResponse.jobPostingId( application.getJobPostingId() );
        summaryResponse.jobTitle( application.getJobTitle() );
        summaryResponse.lastStatusChangedAt( application.getLastStatusChangedAt() );
        summaryResponse.reference( application.getReference() );
        summaryResponse.status( application.getStatus() );
        summaryResponse.submittedAt( application.getSubmittedAt() );

        return summaryResponse.build();
    }

    @Override
    public List<ApplicationDTO.SummaryResponse> toSummaryResponseList(List<Application> applications) {
        if ( applications == null ) {
            return null;
        }

        List<ApplicationDTO.SummaryResponse> list = new ArrayList<ApplicationDTO.SummaryResponse>( applications.size() );
        for ( Application application : applications ) {
            list.add( toSummaryResponse( application ) );
        }

        return list;
    }

    @Override
    public ApplicationDTO.CandidateView toCandidateView(Application application) {
        if ( application == null ) {
            return null;
        }

        ApplicationDTO.CandidateView.CandidateViewBuilder candidateView = ApplicationDTO.CandidateView.builder();

        candidateView.id( application.getId() );
        candidateView.jobDepartment( application.getJobDepartment() );
        candidateView.jobTitle( application.getJobTitle() );
        candidateView.lastStatusChangedAt( application.getLastStatusChangedAt() );
        candidateView.reference( application.getReference() );
        candidateView.status( application.getStatus() );
        candidateView.statusHistory( applicationStatusHistorySetToStatusHistoryDTOList1( application.getStatusHistory() ) );
        candidateView.submittedAt( application.getSubmittedAt() );

        return candidateView.build();
    }

    @Override
    public List<ApplicationDTO.CandidateView> toCandidateViewList(List<Application> applications) {
        if ( applications == null ) {
            return null;
        }

        List<ApplicationDTO.CandidateView> list = new ArrayList<ApplicationDTO.CandidateView>( applications.size() );
        for ( Application application : applications ) {
            list.add( toCandidateView( application ) );
        }

        return list;
    }

    @Override
    public ApplicationDTO.DashboardView toDashboardView(Application application) {
        if ( application == null ) {
            return null;
        }

        ApplicationDTO.DashboardView.DashboardViewBuilder dashboardView = ApplicationDTO.DashboardView.builder();

        dashboardView.daysInCurrentStatus( calculateDaysInStatus( application.getLastStatusChangedAt() ) );
        Evaluation.EvaluationRecommendation recommendation = applicationEvaluationRecommendation( application );
        if ( recommendation != null ) {
            dashboardView.aiRecommendation( recommendation.name() );
        }
        dashboardView.aiScore( application.getAiScore() );
        dashboardView.candidateName( application.getCandidateName() );
        dashboardView.id( application.getId() );
        dashboardView.jobTitle( application.getJobTitle() );
        dashboardView.reference( application.getReference() );
        dashboardView.status( application.getStatus() );
        dashboardView.submittedAt( application.getSubmittedAt() );

        dashboardView.hasDocuments( application.getResumeDocumentId() != null );

        return dashboardView.build();
    }

    @Override
    public StatusHistoryDTO toStatusHistoryDTO(ApplicationStatusHistory history) {
        if ( history == null ) {
            return null;
        }

        StatusHistoryDTO.StatusHistoryDTOBuilder statusHistoryDTO = StatusHistoryDTO.builder();

        statusHistoryDTO.changedAt( history.getChangedAt() );
        statusHistoryDTO.changedBy( history.getChangedBy() );
        statusHistoryDTO.isAutomaticDecision( history.getIsAutomaticDecision() );
        statusHistoryDTO.isSystemChange( history.getIsSystemChange() );
        statusHistoryDTO.newStatus( history.getNewStatus() );
        statusHistoryDTO.previousStatus( history.getPreviousStatus() );
        statusHistoryDTO.reason( history.getReason() );

        return statusHistoryDTO.build();
    }

    @Override
    public List<StatusHistoryDTO> toStatusHistoryDTOList(List<ApplicationStatusHistory> history) {
        if ( history == null ) {
            return null;
        }

        List<StatusHistoryDTO> list = new ArrayList<StatusHistoryDTO>( history.size() );
        for ( ApplicationStatusHistory applicationStatusHistory : history ) {
            list.add( toStatusHistoryDTO( applicationStatusHistory ) );
        }

        return list;
    }

    protected List<StatusHistoryDTO> applicationStatusHistorySetToStatusHistoryDTOList(Set<ApplicationStatusHistory> set) {
        if ( set == null ) {
            return null;
        }

        List<StatusHistoryDTO> list = new ArrayList<StatusHistoryDTO>( set.size() );
        for ( ApplicationStatusHistory applicationStatusHistory : set ) {
            list.add( toStatusHistoryDTO( applicationStatusHistory ) );
        }

        return list;
    }

    protected List<StatusHistoryDTO> applicationStatusHistorySetToStatusHistoryDTOList1(Set<ApplicationStatusHistory> set) {
        if ( set == null ) {
            return null;
        }

        List<StatusHistoryDTO> list = new ArrayList<StatusHistoryDTO>( set.size() );
        for ( ApplicationStatusHistory applicationStatusHistory : set ) {
            list.add( toStatusHistoryDTO( applicationStatusHistory ) );
        }

        return list;
    }

    private Evaluation.EvaluationRecommendation applicationEvaluationRecommendation(Application application) {
        if ( application == null ) {
            return null;
        }
        Evaluation evaluation = application.getEvaluation();
        if ( evaluation == null ) {
            return null;
        }
        Evaluation.EvaluationRecommendation recommendation = evaluation.getRecommendation();
        if ( recommendation == null ) {
            return null;
        }
        return recommendation;
    }
}
