package com.PFE2025.user_service.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO léger pour les listes de profils candidats (optimisation performance)
 * Contient uniquement les champs essentiels pour l'affichage en liste
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CandidateProfileSummaryResponse {
    
    private String id;
    private String keycloakId;
    
    // Informations de base
    private String firstName;
    private String lastName;
    private String fullName;
    private String email;
    private String status;
    
    // Informations professionnelles essentielles
    private String location;
    private String seniorityLevel;
    private Integer yearsOfExperience;
    private List<String> skills;
    private List<String> preferredCategories;
    
    // Photo pour l'affichage
    private String photo;
    
    // Scores ATS pour le tri
    private Integer overallScore;
    private String atsCompatibility;
    
    // Métadonnées
    private String createdAt;
    private String updatedAt;
}
