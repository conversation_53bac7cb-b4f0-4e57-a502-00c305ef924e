package com.pfe2025.jobrequisitionservice.mapper;

import com.pfe2025.jobrequisitionservice.dto.StatusHistoryResponseDto;
import com.pfe2025.jobrequisitionservice.model.RequisitionStatusHistory;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T02:01:55+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class StatusHistoryMapperImpl implements StatusHistoryMapper {

    @Override
    public StatusHistoryResponseDto toStatusHistoryDto(RequisitionStatusHistory entity) {
        if ( entity == null ) {
            return null;
        }

        StatusHistoryResponseDto.StatusHistoryResponseDtoBuilder statusHistoryResponseDto = StatusHistoryResponseDto.builder();

        statusHistoryResponseDto.changedAt( entity.getChangedAt() );
        statusHistoryResponseDto.changedByName( entity.getChangedByName() );
        statusHistoryResponseDto.comments( entity.getComments() );
        statusHistoryResponseDto.newStatus( entity.getNewStatus() );
        statusHistoryResponseDto.oldStatus( entity.getOldStatus() );

        return statusHistoryResponseDto.build();
    }

    @Override
    public Set<StatusHistoryResponseDto> toStatusHistoryDtoSet(Set<RequisitionStatusHistory> entities) {
        if ( entities == null ) {
            return null;
        }

        Set<StatusHistoryResponseDto> set = new LinkedHashSet<StatusHistoryResponseDto>( Math.max( (int) ( entities.size() / .75f ) + 1, 16 ) );
        for ( RequisitionStatusHistory requisitionStatusHistory : entities ) {
            set.add( toStatusHistoryDto( requisitionStatusHistory ) );
        }

        return set;
    }

    @Override
    public List<StatusHistoryResponseDto> toStatusHistoryDtoList(List<RequisitionStatusHistory> entities) {
        if ( entities == null ) {
            return null;
        }

        List<StatusHistoryResponseDto> list = new ArrayList<StatusHistoryResponseDto>( entities.size() );
        for ( RequisitionStatusHistory requisitionStatusHistory : entities ) {
            list.add( toStatusHistoryDto( requisitionStatusHistory ) );
        }

        List<StatusHistoryResponseDto> target = sortByChangedAtDesc( list );
        if ( target != null ) {
            return target;
        }

        return list;
    }
}
