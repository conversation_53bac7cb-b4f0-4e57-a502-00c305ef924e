<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MongoConfiguration">
    <option name="serverConfigurations">
      <set>
        <ServerConfiguration>
          <option name="authenticationDatabase" value="admin" />
          <option name="authenticationMechanism" value="SCRAM-SHA-1" />
          <option name="label" value="MS MongoDB" />
          <option name="password" value="admin" />
          <option name="serverUrls">
            <list>
              <option value="localhost:27017" />
            </list>
          </option>
          <option name="shellWorkingDir" value="null" />
          <option name="sshTunnelingConfiguration">
            <SshTunnelingConfiguration />
          </option>
          <option name="username" value="admin" />
        </ServerConfiguration>
      </set>
    </option>
  </component>
</project>