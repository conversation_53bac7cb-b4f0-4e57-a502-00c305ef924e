package com.PFE2025.user_service.util;

import com.PFE2025.user_service.dto.request.RhAdminCreateRequest;
import com.PFE2025.user_service.dto.request.RhAdminUpdateRequest;
import com.PFE2025.user_service.dto.request.UserCreateRequest;
import com.PFE2025.user_service.dto.response.AuthServiceUserDTO;
import com.PFE2025.user_service.dto.response.RhAdminProfileResponse;
import com.PFE2025.user_service.model.RhAdminProfile;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T14:39:57+0100",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.8 (Oracle Corporation)"
)
@Component
public class RhAdminProfileMapperImpl implements RhAdminProfileMapper {

    @Override
    public RhAdminProfile fromCreateRequest(RhAdminCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        RhAdminProfile.RhAdminProfileBuilder<?, ?> rhAdminProfile = RhAdminProfile.builder();

        if ( request.getFirstName() != null ) {
            rhAdminProfile.firstName( request.getFirstName() );
        }
        if ( request.getLastName() != null ) {
            rhAdminProfile.lastName( request.getLastName() );
        }
        if ( request.getEmail() != null ) {
            rhAdminProfile.email( request.getEmail() );
        }
        if ( request.getLocation() != null ) {
            rhAdminProfile.location( request.getLocation() );
        }
        if ( request.getPhone() != null ) {
            rhAdminProfile.phone( request.getPhone() );
        }
        if ( request.getDepartment() != null ) {
            rhAdminProfile.department( request.getDepartment() );
        }
        List<String> list = request.getHrSpecializations();
        if ( list != null ) {
            rhAdminProfile.hrSpecializations( new ArrayList<String>( list ) );
        }
        if ( request.getRecruitmentExperience() != null ) {
            rhAdminProfile.recruitmentExperience( request.getRecruitmentExperience() );
        }
        List<String> list1 = request.getCompaniesWorked();
        if ( list1 != null ) {
            rhAdminProfile.companiesWorked( new ArrayList<String>( list1 ) );
        }
        List<String> list2 = request.getCertifications();
        if ( list2 != null ) {
            rhAdminProfile.certifications( new ArrayList<String>( list2 ) );
        }
        List<String> list3 = request.getLanguages();
        if ( list3 != null ) {
            rhAdminProfile.languages( new ArrayList<String>( list3 ) );
        }
        if ( request.getAccessLevel() != null ) {
            rhAdminProfile.accessLevel( request.getAccessLevel() );
        }
        List<String> list4 = request.getResponsibleFor();
        if ( list4 != null ) {
            rhAdminProfile.responsibleFor( new ArrayList<String>( list4 ) );
        }
        if ( request.getEmployeesManaged() != null ) {
            rhAdminProfile.employeesManaged( request.getEmployeesManaged() );
        }
        if ( request.getRecruitmentQuota() != null ) {
            rhAdminProfile.recruitmentQuota( request.getRecruitmentQuota() );
        }

        rhAdminProfile.userType( "INTERNAL" );

        return rhAdminProfile.build();
    }

    @Override
    public void updateFromRequest(RhAdminUpdateRequest request, RhAdminProfile profile) {
        if ( request == null ) {
            return;
        }

        if ( request.getFirstName() != null ) {
            profile.setFirstName( request.getFirstName() );
        }
        if ( request.getLastName() != null ) {
            profile.setLastName( request.getLastName() );
        }
        if ( request.getEmail() != null ) {
            profile.setEmail( request.getEmail() );
        }
        if ( request.getLocation() != null ) {
            profile.setLocation( request.getLocation() );
        }
        if ( request.getPhone() != null ) {
            profile.setPhone( request.getPhone() );
        }
        if ( request.getDepartment() != null ) {
            profile.setDepartment( request.getDepartment() );
        }
        if ( profile.getHrSpecializations() != null ) {
            List<String> list = request.getHrSpecializations();
            if ( list != null ) {
                profile.getHrSpecializations().clear();
                profile.getHrSpecializations().addAll( list );
            }
        }
        else {
            List<String> list = request.getHrSpecializations();
            if ( list != null ) {
                profile.setHrSpecializations( new ArrayList<String>( list ) );
            }
        }
        if ( request.getRecruitmentExperience() != null ) {
            profile.setRecruitmentExperience( request.getRecruitmentExperience() );
        }
        if ( profile.getCompaniesWorked() != null ) {
            List<String> list1 = request.getCompaniesWorked();
            if ( list1 != null ) {
                profile.getCompaniesWorked().clear();
                profile.getCompaniesWorked().addAll( list1 );
            }
        }
        else {
            List<String> list1 = request.getCompaniesWorked();
            if ( list1 != null ) {
                profile.setCompaniesWorked( new ArrayList<String>( list1 ) );
            }
        }
        if ( profile.getCertifications() != null ) {
            List<String> list2 = request.getCertifications();
            if ( list2 != null ) {
                profile.getCertifications().clear();
                profile.getCertifications().addAll( list2 );
            }
        }
        else {
            List<String> list2 = request.getCertifications();
            if ( list2 != null ) {
                profile.setCertifications( new ArrayList<String>( list2 ) );
            }
        }
        if ( profile.getLanguages() != null ) {
            List<String> list3 = request.getLanguages();
            if ( list3 != null ) {
                profile.getLanguages().clear();
                profile.getLanguages().addAll( list3 );
            }
        }
        else {
            List<String> list3 = request.getLanguages();
            if ( list3 != null ) {
                profile.setLanguages( new ArrayList<String>( list3 ) );
            }
        }
        if ( request.getAccessLevel() != null ) {
            profile.setAccessLevel( request.getAccessLevel() );
        }
        if ( profile.getResponsibleFor() != null ) {
            List<String> list4 = request.getResponsibleFor();
            if ( list4 != null ) {
                profile.getResponsibleFor().clear();
                profile.getResponsibleFor().addAll( list4 );
            }
        }
        else {
            List<String> list4 = request.getResponsibleFor();
            if ( list4 != null ) {
                profile.setResponsibleFor( new ArrayList<String>( list4 ) );
            }
        }
        if ( request.getEmployeesManaged() != null ) {
            profile.setEmployeesManaged( request.getEmployeesManaged() );
        }
        if ( request.getRecruitmentQuota() != null ) {
            profile.setRecruitmentQuota( request.getRecruitmentQuota() );
        }

        updateFullName( profile );
    }

    @Override
    public RhAdminProfileResponse toResponse(RhAdminProfile profile) {
        if ( profile == null ) {
            return null;
        }

        RhAdminProfileResponse.RhAdminProfileResponseBuilder rhAdminProfileResponse = RhAdminProfileResponse.builder();

        if ( profile.getId() != null ) {
            rhAdminProfileResponse.id( profile.getId() );
        }
        if ( profile.getKeycloakId() != null ) {
            rhAdminProfileResponse.keycloakId( profile.getKeycloakId() );
        }
        if ( profile.getFirstName() != null ) {
            rhAdminProfileResponse.firstName( profile.getFirstName() );
        }
        if ( profile.getLastName() != null ) {
            rhAdminProfileResponse.lastName( profile.getLastName() );
        }
        if ( profile.getFullName() != null ) {
            rhAdminProfileResponse.fullName( profile.getFullName() );
        }
        if ( profile.getEmail() != null ) {
            rhAdminProfileResponse.email( profile.getEmail() );
        }
        List<String> list = profile.getRoles();
        if ( list != null ) {
            rhAdminProfileResponse.roles( new ArrayList<String>( list ) );
        }
        if ( profile.getUserType() != null ) {
            rhAdminProfileResponse.userType( profile.getUserType() );
        }
        if ( profile.getStatus() != null ) {
            rhAdminProfileResponse.status( profile.getStatus() );
        }
        if ( profile.getLocation() != null ) {
            rhAdminProfileResponse.location( profile.getLocation() );
        }
        if ( profile.getPhone() != null ) {
            rhAdminProfileResponse.phone( profile.getPhone() );
        }
        if ( profile.getDepartment() != null ) {
            rhAdminProfileResponse.department( profile.getDepartment() );
        }
        List<String> list1 = profile.getHrSpecializations();
        if ( list1 != null ) {
            rhAdminProfileResponse.hrSpecializations( new ArrayList<String>( list1 ) );
        }
        if ( profile.getRecruitmentExperience() != null ) {
            rhAdminProfileResponse.recruitmentExperience( profile.getRecruitmentExperience() );
        }
        List<String> list2 = profile.getCompaniesWorked();
        if ( list2 != null ) {
            rhAdminProfileResponse.companiesWorked( new ArrayList<String>( list2 ) );
        }
        List<String> list3 = profile.getCertifications();
        if ( list3 != null ) {
            rhAdminProfileResponse.certifications( new ArrayList<String>( list3 ) );
        }
        List<String> list4 = profile.getLanguages();
        if ( list4 != null ) {
            rhAdminProfileResponse.languages( new ArrayList<String>( list4 ) );
        }
        if ( profile.getAccessLevel() != null ) {
            rhAdminProfileResponse.accessLevel( profile.getAccessLevel() );
        }
        List<String> list5 = profile.getResponsibleFor();
        if ( list5 != null ) {
            rhAdminProfileResponse.responsibleFor( new ArrayList<String>( list5 ) );
        }
        if ( profile.getEmployeesManaged() != null ) {
            rhAdminProfileResponse.employeesManaged( profile.getEmployeesManaged() );
        }
        if ( profile.getRecruitmentQuota() != null ) {
            rhAdminProfileResponse.recruitmentQuota( profile.getRecruitmentQuota() );
        }
        if ( profile.getCreatedAt() != null ) {
            rhAdminProfileResponse.createdAt( profile.getCreatedAt() );
        }
        if ( profile.getUpdatedAt() != null ) {
            rhAdminProfileResponse.updatedAt( profile.getUpdatedAt() );
        }

        rhAdminProfileResponse.enriched( profile.isEnriched() );

        return rhAdminProfileResponse.build();
    }

    @Override
    public RhAdminProfileResponse enrichWithAuthData(RhAdminProfile profile, AuthServiceUserDTO authUser) {
        if ( profile == null && authUser == null ) {
            return null;
        }

        RhAdminProfileResponse.RhAdminProfileResponseBuilder rhAdminProfileResponse = RhAdminProfileResponse.builder();

        if ( profile != null ) {
            if ( profile.getId() != null ) {
                rhAdminProfileResponse.id( profile.getId() );
            }
            if ( profile.getKeycloakId() != null ) {
                rhAdminProfileResponse.keycloakId( profile.getKeycloakId() );
            }
            if ( profile.getUserType() != null ) {
                rhAdminProfileResponse.userType( profile.getUserType() );
            }
            if ( profile.getLocation() != null ) {
                rhAdminProfileResponse.location( profile.getLocation() );
            }
            if ( profile.getPhone() != null ) {
                rhAdminProfileResponse.phone( profile.getPhone() );
            }
            if ( profile.getDepartment() != null ) {
                rhAdminProfileResponse.department( profile.getDepartment() );
            }
            List<String> list1 = profile.getHrSpecializations();
            if ( list1 != null ) {
                rhAdminProfileResponse.hrSpecializations( new ArrayList<String>( list1 ) );
            }
            if ( profile.getRecruitmentExperience() != null ) {
                rhAdminProfileResponse.recruitmentExperience( profile.getRecruitmentExperience() );
            }
            List<String> list2 = profile.getCompaniesWorked();
            if ( list2 != null ) {
                rhAdminProfileResponse.companiesWorked( new ArrayList<String>( list2 ) );
            }
            List<String> list3 = profile.getCertifications();
            if ( list3 != null ) {
                rhAdminProfileResponse.certifications( new ArrayList<String>( list3 ) );
            }
            List<String> list4 = profile.getLanguages();
            if ( list4 != null ) {
                rhAdminProfileResponse.languages( new ArrayList<String>( list4 ) );
            }
            if ( profile.getAccessLevel() != null ) {
                rhAdminProfileResponse.accessLevel( profile.getAccessLevel() );
            }
            List<String> list5 = profile.getResponsibleFor();
            if ( list5 != null ) {
                rhAdminProfileResponse.responsibleFor( new ArrayList<String>( list5 ) );
            }
            if ( profile.getEmployeesManaged() != null ) {
                rhAdminProfileResponse.employeesManaged( profile.getEmployeesManaged() );
            }
            if ( profile.getRecruitmentQuota() != null ) {
                rhAdminProfileResponse.recruitmentQuota( profile.getRecruitmentQuota() );
            }
            if ( profile.getCreatedAt() != null ) {
                rhAdminProfileResponse.createdAt( profile.getCreatedAt() );
            }
            if ( profile.getUpdatedAt() != null ) {
                rhAdminProfileResponse.updatedAt( profile.getUpdatedAt() );
            }
            rhAdminProfileResponse.enriched( profile.isEnriched() );
        }
        if ( authUser != null ) {
            List<String> list = setToList( authUser.getRoles() );
            if ( list != null ) {
                rhAdminProfileResponse.roles( list );
            }
            if ( authUser.getFirstName() != null ) {
                rhAdminProfileResponse.firstName( authUser.getFirstName() );
            }
            if ( authUser.getLastName() != null ) {
                rhAdminProfileResponse.lastName( authUser.getLastName() );
            }
            if ( authUser.getEmail() != null ) {
                rhAdminProfileResponse.email( authUser.getEmail() );
            }
            if ( authUser.getFullName() != null ) {
                rhAdminProfileResponse.fullName( authUser.getFullName() );
            }
        }
        rhAdminProfileResponse.status( authUser.isEnabled() ? "ACTIVE" : "INACTIVE" );

        return rhAdminProfileResponse.build();
    }

    @Override
    public UserCreateRequest toUserCreateRequest(RhAdminCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        UserCreateRequest.UserCreateRequestBuilder userCreateRequest = UserCreateRequest.builder();

        if ( request.getDepartment() != null ) {
            userCreateRequest.department( request.getDepartment() );
        }
        if ( request.getUsername() != null ) {
            userCreateRequest.username( request.getUsername() );
        }
        if ( request.getFirstName() != null ) {
            userCreateRequest.firstName( request.getFirstName() );
        }
        if ( request.getLastName() != null ) {
            userCreateRequest.lastName( request.getLastName() );
        }
        if ( request.getEmail() != null ) {
            userCreateRequest.email( request.getEmail() );
        }
        if ( request.getPassword() != null ) {
            userCreateRequest.password( request.getPassword() );
        }
        if ( request.getPhone() != null ) {
            userCreateRequest.phone( request.getPhone() );
        }
        if ( request.getEnabled() != null ) {
            userCreateRequest.enabled( request.getEnabled() );
        }
        if ( request.getEmailVerified() != null ) {
            userCreateRequest.emailVerified( request.getEmailVerified() );
        }

        userCreateRequest.roles( java.util.Set.of("RH_ADMIN") );
        userCreateRequest.userType( "INTERNAL" );

        return userCreateRequest.build();
    }
}
