package com.PFE2025.user_service.util;

import com.PFE2025.user_service.dto.request.RhAdminCreateRequest;
import com.PFE2025.user_service.dto.request.RhAdminUpdateRequest;
import com.PFE2025.user_service.dto.request.UserCreateRequest;
import com.PFE2025.user_service.dto.response.AuthServiceUserDTO;
import com.PFE2025.user_service.dto.response.RhAdminProfileResponse;
import com.PFE2025.user_service.model.RhAdminProfile;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T02:02:00+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class RhAdminProfileMapperImpl implements RhAdminProfileMapper {

    @Override
    public RhAdminProfile fromCreateRequest(RhAdminCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        RhAdminProfile.RhAdminProfileBuilder<?, ?> rhAdminProfile = RhAdminProfile.builder();

        if ( request.getAccessLevel() != null ) {
            rhAdminProfile.accessLevel( request.getAccessLevel() );
        }
        List<String> list = request.getCertifications();
        if ( list != null ) {
            rhAdminProfile.certifications( new ArrayList<String>( list ) );
        }
        List<String> list1 = request.getCompaniesWorked();
        if ( list1 != null ) {
            rhAdminProfile.companiesWorked( new ArrayList<String>( list1 ) );
        }
        if ( request.getDepartment() != null ) {
            rhAdminProfile.department( request.getDepartment() );
        }
        if ( request.getEmail() != null ) {
            rhAdminProfile.email( request.getEmail() );
        }
        if ( request.getEmployeesManaged() != null ) {
            rhAdminProfile.employeesManaged( request.getEmployeesManaged() );
        }
        if ( request.getFirstName() != null ) {
            rhAdminProfile.firstName( request.getFirstName() );
        }
        List<String> list2 = request.getHrSpecializations();
        if ( list2 != null ) {
            rhAdminProfile.hrSpecializations( new ArrayList<String>( list2 ) );
        }
        List<String> list3 = request.getLanguages();
        if ( list3 != null ) {
            rhAdminProfile.languages( new ArrayList<String>( list3 ) );
        }
        if ( request.getLastName() != null ) {
            rhAdminProfile.lastName( request.getLastName() );
        }
        if ( request.getLocation() != null ) {
            rhAdminProfile.location( request.getLocation() );
        }
        if ( request.getPhone() != null ) {
            rhAdminProfile.phone( request.getPhone() );
        }
        if ( request.getRecruitmentExperience() != null ) {
            rhAdminProfile.recruitmentExperience( request.getRecruitmentExperience() );
        }
        if ( request.getRecruitmentQuota() != null ) {
            rhAdminProfile.recruitmentQuota( request.getRecruitmentQuota() );
        }
        List<String> list4 = request.getResponsibleFor();
        if ( list4 != null ) {
            rhAdminProfile.responsibleFor( new ArrayList<String>( list4 ) );
        }

        rhAdminProfile.userType( "INTERNAL" );

        return rhAdminProfile.build();
    }

    @Override
    public void updateFromRequest(RhAdminUpdateRequest request, RhAdminProfile profile) {
        if ( request == null ) {
            return;
        }

        if ( request.getAccessLevel() != null ) {
            profile.setAccessLevel( request.getAccessLevel() );
        }
        if ( profile.getCertifications() != null ) {
            List<String> list = request.getCertifications();
            if ( list != null ) {
                profile.getCertifications().clear();
                profile.getCertifications().addAll( list );
            }
        }
        else {
            List<String> list = request.getCertifications();
            if ( list != null ) {
                profile.setCertifications( new ArrayList<String>( list ) );
            }
        }
        if ( profile.getCompaniesWorked() != null ) {
            List<String> list1 = request.getCompaniesWorked();
            if ( list1 != null ) {
                profile.getCompaniesWorked().clear();
                profile.getCompaniesWorked().addAll( list1 );
            }
        }
        else {
            List<String> list1 = request.getCompaniesWorked();
            if ( list1 != null ) {
                profile.setCompaniesWorked( new ArrayList<String>( list1 ) );
            }
        }
        if ( request.getDepartment() != null ) {
            profile.setDepartment( request.getDepartment() );
        }
        if ( request.getEmail() != null ) {
            profile.setEmail( request.getEmail() );
        }
        if ( request.getEmployeesManaged() != null ) {
            profile.setEmployeesManaged( request.getEmployeesManaged() );
        }
        if ( request.getFirstName() != null ) {
            profile.setFirstName( request.getFirstName() );
        }
        if ( profile.getHrSpecializations() != null ) {
            List<String> list2 = request.getHrSpecializations();
            if ( list2 != null ) {
                profile.getHrSpecializations().clear();
                profile.getHrSpecializations().addAll( list2 );
            }
        }
        else {
            List<String> list2 = request.getHrSpecializations();
            if ( list2 != null ) {
                profile.setHrSpecializations( new ArrayList<String>( list2 ) );
            }
        }
        if ( profile.getLanguages() != null ) {
            List<String> list3 = request.getLanguages();
            if ( list3 != null ) {
                profile.getLanguages().clear();
                profile.getLanguages().addAll( list3 );
            }
        }
        else {
            List<String> list3 = request.getLanguages();
            if ( list3 != null ) {
                profile.setLanguages( new ArrayList<String>( list3 ) );
            }
        }
        if ( request.getLastName() != null ) {
            profile.setLastName( request.getLastName() );
        }
        if ( request.getLocation() != null ) {
            profile.setLocation( request.getLocation() );
        }
        if ( request.getPhone() != null ) {
            profile.setPhone( request.getPhone() );
        }
        if ( request.getRecruitmentExperience() != null ) {
            profile.setRecruitmentExperience( request.getRecruitmentExperience() );
        }
        if ( request.getRecruitmentQuota() != null ) {
            profile.setRecruitmentQuota( request.getRecruitmentQuota() );
        }
        if ( profile.getResponsibleFor() != null ) {
            List<String> list4 = request.getResponsibleFor();
            if ( list4 != null ) {
                profile.getResponsibleFor().clear();
                profile.getResponsibleFor().addAll( list4 );
            }
        }
        else {
            List<String> list4 = request.getResponsibleFor();
            if ( list4 != null ) {
                profile.setResponsibleFor( new ArrayList<String>( list4 ) );
            }
        }

        updateFullName( profile );
    }

    @Override
    public RhAdminProfileResponse toResponse(RhAdminProfile profile) {
        if ( profile == null ) {
            return null;
        }

        RhAdminProfileResponse.RhAdminProfileResponseBuilder rhAdminProfileResponse = RhAdminProfileResponse.builder();

        if ( profile.getAccessLevel() != null ) {
            rhAdminProfileResponse.accessLevel( profile.getAccessLevel() );
        }
        List<String> list = profile.getCertifications();
        if ( list != null ) {
            rhAdminProfileResponse.certifications( new ArrayList<String>( list ) );
        }
        List<String> list1 = profile.getCompaniesWorked();
        if ( list1 != null ) {
            rhAdminProfileResponse.companiesWorked( new ArrayList<String>( list1 ) );
        }
        if ( profile.getCreatedAt() != null ) {
            rhAdminProfileResponse.createdAt( profile.getCreatedAt() );
        }
        if ( profile.getDepartment() != null ) {
            rhAdminProfileResponse.department( profile.getDepartment() );
        }
        if ( profile.getEmail() != null ) {
            rhAdminProfileResponse.email( profile.getEmail() );
        }
        if ( profile.getEmployeesManaged() != null ) {
            rhAdminProfileResponse.employeesManaged( profile.getEmployeesManaged() );
        }
        if ( profile.getFirstName() != null ) {
            rhAdminProfileResponse.firstName( profile.getFirstName() );
        }
        if ( profile.getFullName() != null ) {
            rhAdminProfileResponse.fullName( profile.getFullName() );
        }
        List<String> list2 = profile.getHrSpecializations();
        if ( list2 != null ) {
            rhAdminProfileResponse.hrSpecializations( new ArrayList<String>( list2 ) );
        }
        if ( profile.getId() != null ) {
            rhAdminProfileResponse.id( profile.getId() );
        }
        if ( profile.getKeycloakId() != null ) {
            rhAdminProfileResponse.keycloakId( profile.getKeycloakId() );
        }
        List<String> list3 = profile.getLanguages();
        if ( list3 != null ) {
            rhAdminProfileResponse.languages( new ArrayList<String>( list3 ) );
        }
        if ( profile.getLastName() != null ) {
            rhAdminProfileResponse.lastName( profile.getLastName() );
        }
        if ( profile.getLocation() != null ) {
            rhAdminProfileResponse.location( profile.getLocation() );
        }
        if ( profile.getPhone() != null ) {
            rhAdminProfileResponse.phone( profile.getPhone() );
        }
        if ( profile.getRecruitmentExperience() != null ) {
            rhAdminProfileResponse.recruitmentExperience( profile.getRecruitmentExperience() );
        }
        if ( profile.getRecruitmentQuota() != null ) {
            rhAdminProfileResponse.recruitmentQuota( profile.getRecruitmentQuota() );
        }
        List<String> list4 = profile.getResponsibleFor();
        if ( list4 != null ) {
            rhAdminProfileResponse.responsibleFor( new ArrayList<String>( list4 ) );
        }
        List<String> list5 = profile.getRoles();
        if ( list5 != null ) {
            rhAdminProfileResponse.roles( new ArrayList<String>( list5 ) );
        }
        if ( profile.getStatus() != null ) {
            rhAdminProfileResponse.status( profile.getStatus() );
        }
        if ( profile.getUpdatedAt() != null ) {
            rhAdminProfileResponse.updatedAt( profile.getUpdatedAt() );
        }
        if ( profile.getUserType() != null ) {
            rhAdminProfileResponse.userType( profile.getUserType() );
        }

        rhAdminProfileResponse.enriched( profile.isEnriched() );

        return rhAdminProfileResponse.build();
    }

    @Override
    public RhAdminProfileResponse enrichWithAuthData(RhAdminProfile profile, AuthServiceUserDTO authUser) {
        if ( profile == null && authUser == null ) {
            return null;
        }

        RhAdminProfileResponse.RhAdminProfileResponseBuilder rhAdminProfileResponse = RhAdminProfileResponse.builder();

        if ( profile != null ) {
            if ( profile.getAccessLevel() != null ) {
                rhAdminProfileResponse.accessLevel( profile.getAccessLevel() );
            }
            List<String> list1 = profile.getCertifications();
            if ( list1 != null ) {
                rhAdminProfileResponse.certifications( new ArrayList<String>( list1 ) );
            }
            List<String> list2 = profile.getCompaniesWorked();
            if ( list2 != null ) {
                rhAdminProfileResponse.companiesWorked( new ArrayList<String>( list2 ) );
            }
            if ( profile.getCreatedAt() != null ) {
                rhAdminProfileResponse.createdAt( profile.getCreatedAt() );
            }
            if ( profile.getDepartment() != null ) {
                rhAdminProfileResponse.department( profile.getDepartment() );
            }
            if ( profile.getEmployeesManaged() != null ) {
                rhAdminProfileResponse.employeesManaged( profile.getEmployeesManaged() );
            }
            rhAdminProfileResponse.enriched( profile.isEnriched() );
            List<String> list3 = profile.getHrSpecializations();
            if ( list3 != null ) {
                rhAdminProfileResponse.hrSpecializations( new ArrayList<String>( list3 ) );
            }
            if ( profile.getId() != null ) {
                rhAdminProfileResponse.id( profile.getId() );
            }
            if ( profile.getKeycloakId() != null ) {
                rhAdminProfileResponse.keycloakId( profile.getKeycloakId() );
            }
            List<String> list4 = profile.getLanguages();
            if ( list4 != null ) {
                rhAdminProfileResponse.languages( new ArrayList<String>( list4 ) );
            }
            if ( profile.getLocation() != null ) {
                rhAdminProfileResponse.location( profile.getLocation() );
            }
            if ( profile.getPhone() != null ) {
                rhAdminProfileResponse.phone( profile.getPhone() );
            }
            if ( profile.getRecruitmentExperience() != null ) {
                rhAdminProfileResponse.recruitmentExperience( profile.getRecruitmentExperience() );
            }
            if ( profile.getRecruitmentQuota() != null ) {
                rhAdminProfileResponse.recruitmentQuota( profile.getRecruitmentQuota() );
            }
            List<String> list5 = profile.getResponsibleFor();
            if ( list5 != null ) {
                rhAdminProfileResponse.responsibleFor( new ArrayList<String>( list5 ) );
            }
            if ( profile.getUpdatedAt() != null ) {
                rhAdminProfileResponse.updatedAt( profile.getUpdatedAt() );
            }
            if ( profile.getUserType() != null ) {
                rhAdminProfileResponse.userType( profile.getUserType() );
            }
        }
        if ( authUser != null ) {
            List<String> list = setToList( authUser.getRoles() );
            if ( list != null ) {
                rhAdminProfileResponse.roles( list );
            }
            if ( authUser.getFirstName() != null ) {
                rhAdminProfileResponse.firstName( authUser.getFirstName() );
            }
            if ( authUser.getLastName() != null ) {
                rhAdminProfileResponse.lastName( authUser.getLastName() );
            }
            if ( authUser.getEmail() != null ) {
                rhAdminProfileResponse.email( authUser.getEmail() );
            }
            if ( authUser.getFullName() != null ) {
                rhAdminProfileResponse.fullName( authUser.getFullName() );
            }
        }
        rhAdminProfileResponse.status( authUser.isEnabled() ? "ACTIVE" : "INACTIVE" );

        return rhAdminProfileResponse.build();
    }

    @Override
    public UserCreateRequest toUserCreateRequest(RhAdminCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        UserCreateRequest.UserCreateRequestBuilder userCreateRequest = UserCreateRequest.builder();

        if ( request.getDepartment() != null ) {
            userCreateRequest.department( request.getDepartment() );
        }
        if ( request.getEmail() != null ) {
            userCreateRequest.email( request.getEmail() );
        }
        if ( request.getEmailVerified() != null ) {
            userCreateRequest.emailVerified( request.getEmailVerified() );
        }
        if ( request.getEnabled() != null ) {
            userCreateRequest.enabled( request.getEnabled() );
        }
        if ( request.getFirstName() != null ) {
            userCreateRequest.firstName( request.getFirstName() );
        }
        if ( request.getLastName() != null ) {
            userCreateRequest.lastName( request.getLastName() );
        }
        if ( request.getPassword() != null ) {
            userCreateRequest.password( request.getPassword() );
        }
        if ( request.getPhone() != null ) {
            userCreateRequest.phone( request.getPhone() );
        }
        if ( request.getUsername() != null ) {
            userCreateRequest.username( request.getUsername() );
        }

        userCreateRequest.roles( java.util.Set.of("RH_ADMIN") );
        userCreateRequest.userType( "INTERNAL" );

        return userCreateRequest.build();
    }
}
