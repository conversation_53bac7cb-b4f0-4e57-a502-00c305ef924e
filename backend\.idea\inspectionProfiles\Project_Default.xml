<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="AutoCloseableResource" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="METHOD_MATCHER_CONFIG" value="java.util.Formatter,format,java.io.Writer,append,com.google.common.base.Preconditions,checkNotNull,org.hibernate.Session,close,java.io.PrintWriter,printf,java.io.PrintStream,printf,com.PFE2025.auth_service.config.KeycloakConfig,keycloakAdminClient" />
    </inspection_tool>
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="56" name="Java" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="IncorrectHttpHeaderInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="customHeaders">
        <set>
          <option value="X-RateLimit-Remaining" />
        </set>
      </option>
    </inspection_tool>
    <inspection_tool class="SizeReplaceableByIsEmpty" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredTypes">
        <set>
          <option value="java.lang.StringBuilder" />
        </set>
      </option>
    </inspection_tool>
    <inspection_tool class="SpringJavaInjectionPointsAutowiringInspection" enabled="false" level="ERROR" enabled_by_default="false" />
  </profile>
</component>