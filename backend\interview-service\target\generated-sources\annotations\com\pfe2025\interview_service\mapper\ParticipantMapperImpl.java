package com.pfe2025.interview_service.mapper;

import com.pfe2025.interview_service.dto.ParticipantDTO;
import com.pfe2025.interview_service.model.InterviewParticipant;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T14:17:51+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ParticipantMapperImpl implements ParticipantMapper {

    @Override
    public ParticipantDTO.ParticipantResponse toResponse(InterviewParticipant participant) {
        if ( participant == null ) {
            return null;
        }

        ParticipantDTO.ParticipantResponse.ParticipantResponseBuilder participantResponse = ParticipantDTO.ParticipantResponse.builder();

        participantResponse.participantStatus( participant.getStatus() );
        participantResponse.participantRole( participant.getRole() );
        participantResponse.id( participant.getId() );
        participantResponse.isOrganizer( participant.getIsOrganizer() );
        participantResponse.isRequired( participant.getIsRequired() );
        participantResponse.notes( participant.getNotes() );
        participantResponse.userEmail( participant.getUserEmail() );
        participantResponse.userId( participant.getUserId() );
        participantResponse.userName( participant.getUserName() );

        return participantResponse.build();
    }

    @Override
    public List<ParticipantDTO.ParticipantResponse> toResponseList(List<InterviewParticipant> participants) {
        if ( participants == null ) {
            return null;
        }

        List<ParticipantDTO.ParticipantResponse> list = new ArrayList<ParticipantDTO.ParticipantResponse>( participants.size() );
        for ( InterviewParticipant interviewParticipant : participants ) {
            list.add( toResponse( interviewParticipant ) );
        }

        return list;
    }

    @Override
    public InterviewParticipant fromRequest(ParticipantDTO.AddParticipantRequest request) {
        if ( request == null ) {
            return null;
        }

        InterviewParticipant.InterviewParticipantBuilder<?, ?> interviewParticipant = InterviewParticipant.builder();

        interviewParticipant.isRequired( request.getIsRequired() );
        interviewParticipant.notes( request.getNotes() );
        interviewParticipant.role( request.getRole() );
        interviewParticipant.userEmail( request.getUserEmail() );
        interviewParticipant.userId( request.getUserId() );
        interviewParticipant.userName( request.getUserName() );

        interviewParticipant.status( InterviewParticipant.ParticipantStatus.INVITED );
        interviewParticipant.createdAt( java.time.LocalDateTime.now() );

        return interviewParticipant.build();
    }
}
