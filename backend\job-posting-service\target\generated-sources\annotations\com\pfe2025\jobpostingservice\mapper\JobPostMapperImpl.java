package com.pfe2025.jobpostingservice.mapper;

import com.pfe2025.jobpostingservice.dto.JobPostDTO;
import com.pfe2025.jobpostingservice.dto.JobPostingSkillDTO;
import com.pfe2025.jobpostingservice.event.RequisitionApprovedEvent;
import com.pfe2025.jobpostingservice.model.JobPost;
import com.pfe2025.jobpostingservice.model.JobPostingSkill;
import com.pfe2025.jobpostingservice.model.PostingMetrics;
import com.pfe2025.jobpostingservice.model.enums.EmploymentType;
import com.pfe2025.jobpostingservice.model.enums.PostingStatus;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-11T02:01:50+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class JobPostMapperImpl implements JobPostMapper {

    @Autowired
    private JobPostingSkillMapper jobPostingSkillMapper;
    @Autowired
    private PostingMetricsMapper postingMetricsMapper;

    @Override
    public JobPostDTO.Response toResponseDto(JobPost jobPost) {
        if ( jobPost == null ) {
            return null;
        }

        JobPostDTO.Response.ResponseBuilder<?, ?> response = JobPostDTO.Response.builder();

        response.skills( jobPostingSkillMapper.toDtoSet( jobPost.getSkills() ) );
        response.metrics( postingMetricsMapper.toDto( jobPost.getMetrics() ) );
        response.benefits( jobPost.getBenefits() );
        response.closedAt( jobPost.getClosedAt() );
        response.createdAt( jobPost.getCreatedAt() );
        response.createdBy( jobPost.getCreatedBy() );
        response.department( jobPost.getDepartment() );
        response.description( jobPost.getDescription() );
        response.displaySalary( jobPost.getDisplaySalary() );
        response.employmentType( jobPost.getEmploymentType() );
        response.expiresAt( jobPost.getExpiresAt() );
        response.id( jobPost.getId() );
        response.location( jobPost.getLocation() );
        response.minExperience( jobPost.getMinExperience() );
        response.publicationLevel( jobPost.getPublicationLevel() );
        response.publishedAt( jobPost.getPublishedAt() );
        response.qualifications( jobPost.getQualifications() );
        response.requisitionId( jobPost.getRequisitionId() );
        response.responsibilities( jobPost.getResponsibilities() );
        response.salaryRangeMax( jobPost.getSalaryRangeMax() );
        response.salaryRangeMin( jobPost.getSalaryRangeMin() );
        response.status( jobPost.getStatus() );
        response.templateId( jobPost.getTemplateId() );
        response.title( jobPost.getTitle() );
        response.version( jobPost.getVersion() );

        return response.build();
    }

    @Override
    public JobPostDTO.Summary toSummaryDto(JobPost jobPost) {
        if ( jobPost == null ) {
            return null;
        }

        JobPostDTO.Summary.SummaryBuilder<?, ?> summary = JobPostDTO.Summary.builder();

        summary.id( jobPost.getId() );
        summary.title( jobPost.getTitle() );
        summary.department( jobPost.getDepartment() );
        summary.location( jobPost.getLocation() );
        summary.employmentType( jobPost.getEmploymentType() );
        summary.status( jobPost.getStatus() );
        summary.createdAt( jobPost.getCreatedAt() );
        summary.publishedAt( jobPost.getPublishedAt() );
        summary.applicationCount( jobPostMetricsTotalApplicationCount( jobPost ) );

        return summary.build();
    }

    @Override
    public JobPostDTO.PublicView toPublicViewDto(JobPost jobPost) {
        if ( jobPost == null ) {
            return null;
        }

        JobPostDTO.PublicView.PublicViewBuilder<?, ?> publicView = JobPostDTO.PublicView.builder();

        publicView.id( jobPost.getId() );
        publicView.title( jobPost.getTitle() );
        publicView.department( jobPost.getDepartment() );
        publicView.location( jobPost.getLocation() );
        publicView.employmentType( jobPost.getEmploymentType() );
        publicView.description( jobPost.getDescription() );
        publicView.responsibilities( jobPost.getResponsibilities() );
        publicView.qualifications( jobPost.getQualifications() );
        publicView.benefits( jobPost.getBenefits() );
        publicView.minExperience( jobPost.getMinExperience() );
        publicView.publishedAt( jobPost.getPublishedAt() );
        publicView.skills( jobPostingSkillMapper.toDtoSet( jobPost.getSkills() ) );

        publicView.salaryRange( formatSalaryRange(jobPost) );

        return publicView.build();
    }

    @Override
    public JobPost toEntity(JobPostDTO.Request dto) {
        if ( dto == null ) {
            return null;
        }

        JobPost.JobPostBuilder<?, ?> jobPost = JobPost.builder();

        jobPost.benefits( dto.getBenefits() );
        jobPost.department( dto.getDepartment() );
        jobPost.description( dto.getDescription() );
        jobPost.displaySalary( dto.getDisplaySalary() );
        jobPost.employmentType( dto.getEmploymentType() );
        jobPost.expiresAt( dto.getExpiresAt() );
        jobPost.location( dto.getLocation() );
        jobPost.minExperience( dto.getMinExperience() );
        jobPost.publicationLevel( dto.getPublicationLevel() );
        jobPost.qualifications( dto.getQualifications() );
        jobPost.requisitionId( dto.getRequisitionId() );
        jobPost.responsibilities( dto.getResponsibilities() );
        jobPost.salaryRangeMax( dto.getSalaryRangeMax() );
        jobPost.salaryRangeMin( dto.getSalaryRangeMin() );
        jobPost.skills( jobPostingSkillDTOSetToJobPostingSkillSet( dto.getSkills() ) );
        jobPost.templateId( dto.getTemplateId() );
        jobPost.title( dto.getTitle() );

        jobPost.status( PostingStatus.DRAFT );

        return jobPost.build();
    }

    @Override
    public JobPost fromRequisitionEvent(RequisitionApprovedEvent event) {
        if ( event == null ) {
            return null;
        }

        JobPost.JobPostBuilder<?, ?> jobPost = JobPost.builder();

        jobPost.requisitionId( event.getRequisitionId() );
        jobPost.createdBy( event.getApprovedBy() );
        jobPost.department( event.getDepartment() );
        jobPost.description( event.getDescription() );
        jobPost.minExperience( event.getMinExperience() );
        jobPost.title( event.getTitle() );

        jobPost.status( PostingStatus.DRAFT );
        jobPost.employmentType( convertEmploymentType(event.getRequiredLevel()) );

        return jobPost.build();
    }

    @Override
    public void updateFromDto(JobPostDTO.Request dto, JobPost jobPost) {
        if ( dto == null ) {
            return;
        }

        jobPost.setBenefits( dto.getBenefits() );
        jobPost.setDepartment( dto.getDepartment() );
        jobPost.setDescription( dto.getDescription() );
        jobPost.setDisplaySalary( dto.getDisplaySalary() );
        jobPost.setEmploymentType( dto.getEmploymentType() );
        jobPost.setExpiresAt( dto.getExpiresAt() );
        jobPost.setLocation( dto.getLocation() );
        jobPost.setMinExperience( dto.getMinExperience() );
        jobPost.setPublicationLevel( dto.getPublicationLevel() );
        jobPost.setQualifications( dto.getQualifications() );
        jobPost.setRequisitionId( dto.getRequisitionId() );
        jobPost.setResponsibilities( dto.getResponsibilities() );
        jobPost.setSalaryRangeMax( dto.getSalaryRangeMax() );
        jobPost.setSalaryRangeMin( dto.getSalaryRangeMin() );
        jobPost.setTemplateId( dto.getTemplateId() );
        jobPost.setTitle( dto.getTitle() );
    }

    @Override
    public List<JobPostDTO.Response> toResponseDtoList(List<JobPost> jobPosts) {
        if ( jobPosts == null ) {
            return null;
        }

        List<JobPostDTO.Response> list = new ArrayList<JobPostDTO.Response>( jobPosts.size() );
        for ( JobPost jobPost : jobPosts ) {
            list.add( toResponseDto( jobPost ) );
        }

        return list;
    }

    @Override
    public List<JobPostDTO.Summary> toSummaryDtoList(List<JobPost> jobPosts) {
        if ( jobPosts == null ) {
            return null;
        }

        List<JobPostDTO.Summary> list = new ArrayList<JobPostDTO.Summary>( jobPosts.size() );
        for ( JobPost jobPost : jobPosts ) {
            list.add( toSummaryDto( jobPost ) );
        }

        return list;
    }

    @Override
    public List<JobPostDTO.PublicView> toPublicViewDtoList(List<JobPost> jobPosts) {
        if ( jobPosts == null ) {
            return null;
        }

        List<JobPostDTO.PublicView> list = new ArrayList<JobPostDTO.PublicView>( jobPosts.size() );
        for ( JobPost jobPost : jobPosts ) {
            list.add( toPublicViewDto( jobPost ) );
        }

        return list;
    }

    private Integer jobPostMetricsTotalApplicationCount(JobPost jobPost) {
        if ( jobPost == null ) {
            return null;
        }
        PostingMetrics metrics = jobPost.getMetrics();
        if ( metrics == null ) {
            return null;
        }
        Integer totalApplicationCount = metrics.getTotalApplicationCount();
        if ( totalApplicationCount == null ) {
            return null;
        }
        return totalApplicationCount;
    }

    protected Set<JobPostingSkill> jobPostingSkillDTOSetToJobPostingSkillSet(Set<JobPostingSkillDTO> set) {
        if ( set == null ) {
            return null;
        }

        Set<JobPostingSkill> set1 = new LinkedHashSet<JobPostingSkill>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( JobPostingSkillDTO jobPostingSkillDTO : set ) {
            set1.add( jobPostingSkillMapper.toEntity( jobPostingSkillDTO ) );
        }

        return set1;
    }
}
